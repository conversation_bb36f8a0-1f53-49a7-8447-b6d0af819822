const express = require('express');
const cors = require('cors');
const WebSocket = require('ws');
const http = require('http');
const axios = require('axios');
const path = require('path');
const multer = require('multer');
const fs = require('fs').promises;
const Tesseract = require('tesseract.js');
const pdfParse = require('pdf-parse');
const mammoth = require('mammoth');
const sharp = require('sharp');
const Jimp = require('jimp');

/**
 * SERVEUR INTELLIGENT RESEARCH SYSTEM
 * Système de recherche intelligent avec LLM Gemini 2.0 Flash
 * Recherches itératives et optimisation du contexte
 */

const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server, path: '/ws' });

// Configuration
const PORT = 4000;
const SEARXNG_URL = 'http://localhost:8889';
const OPENROUTER_API_KEY = '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';

// Modèles LLM avec fallback FONCTIONNELS
const LLM_MODELS = [
    'qwen/qwen3-235b-a22b-07-25:free',
    'google/gemini-2.0-flash-exp:free',
    'meta-llama/llama-3.1-8b-instruct:free',
    'microsoft/phi-3-medium-4k-instruct:free'
];

let currentModelIndex = 0;

// Configuration multer pour upload de fichiers
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadDir = path.join(__dirname, 'uploads');
        // Créer le dossier s'il n'existe pas
        require('fs').mkdirSync(uploadDir, { recursive: true });
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 50 * 1024 * 1024 // 50MB max
    },
    fileFilter: function (req, file, cb) {
        // Accepter tous les types de fichiers pour OCR
        const allowedTypes = [
            'image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/tiff', 'image/webp',
            'application/pdf',
            'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain', 'text/csv',
            'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];

        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('Type de fichier non supporté'), false);
        }
    }
});

// Middleware
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.static(path.join(__dirname, 'public')));
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Stockage des recherches actives et conversations
const activeResearches = new Map();
const clients = new Map();
const chatHistory = new Map(); // Stockage des conversations par chatId

// Base de données persistante simple (fichier JSON)
const fsSync = require('fs');
const DB_FILE = path.join(__dirname, 'conversations.json');

// Charger les conversations au démarrage
function loadConversations() {
    try {
        if (fsSync.existsSync(DB_FILE)) {
            const data = fsSync.readFileSync(DB_FILE, 'utf8');
            const conversations = JSON.parse(data);

            // Charger en mémoire
            Object.entries(conversations).forEach(([chatId, conversation]) => {
                chatHistory.set(chatId, conversation);
            });

            console.log(`📚 ${Object.keys(conversations).length} conversations chargées depuis la base`);
        }
    } catch (error) {
        console.error('❌ Erreur chargement conversations:', error);
    }
}

// Sauvegarder les conversations
function saveConversations() {
    try {
        const conversations = {};
        chatHistory.forEach((conversation, chatId) => {
            conversations[chatId] = conversation;
        });

        fsSync.writeFileSync(DB_FILE, JSON.stringify(conversations, null, 2));
        console.log(`💾 ${Object.keys(conversations).length} conversations sauvegardées`);
    } catch (error) {
        console.error('❌ Erreur sauvegarde conversations:', error);
    }
}

// Charger au démarrage
loadConversations();

// ==================== SYSTÈME OCR COMPLET ====================

// Extraire le texte d'un fichier selon son type
async function extractTextFromFile(filePath, mimeType, originalName) {
    console.log(`🔍 Extraction OCR: ${originalName} (${mimeType})`);

    try {
        switch (mimeType) {
            case 'image/jpeg':
            case 'image/png':
            case 'image/gif':
            case 'image/bmp':
            case 'image/tiff':
            case 'image/webp':
                return await extractTextFromImage(filePath);

            case 'application/pdf':
                return await extractTextFromPDF(filePath);

            case 'application/msword':
            case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                return await extractTextFromWord(filePath);

            case 'text/plain':
                return await extractTextFromTxt(filePath);

            default:
                // Essayer OCR sur le fichier comme image
                return await extractTextFromImage(filePath);
        }
    } catch (error) {
        console.error(`❌ Erreur extraction ${originalName}:`, error);
        throw new Error(`Impossible d'extraire le texte de ${originalName}: ${error.message}`);
    }
}

// OCR sur images avec Tesseract
async function extractTextFromImage(imagePath) {
    console.log('📸 OCR image avec Tesseract...');

    try {
        // Préprocessing de l'image pour améliorer l'OCR
        const processedImagePath = await preprocessImage(imagePath);

        const { data: { text } } = await Tesseract.recognize(
            processedImagePath,
            'fra+eng', // Français + Anglais
            {
                logger: m => {
                    if (m.status === 'recognizing text') {
                        console.log(`OCR Progress: ${Math.round(m.progress * 100)}%`);
                    }
                }
            }
        );

        // Nettoyer le texte extrait
        const cleanText = text
            .replace(/\n\s*\n/g, '\n') // Supprimer les lignes vides multiples
            .replace(/\s+/g, ' ') // Normaliser les espaces
            .trim();

        console.log(`✅ OCR terminé: ${cleanText.length} caractères extraits`);
        return cleanText;

    } catch (error) {
        console.error('❌ Erreur OCR image:', error);
        throw error;
    }
}

// Préprocessing d'image pour améliorer l'OCR
async function preprocessImage(imagePath) {
    const outputPath = imagePath.replace(/\.[^/.]+$/, '_processed.png');

    try {
        await sharp(imagePath)
            .resize(null, 2000, {
                withoutEnlargement: true,
                kernel: sharp.kernel.lanczos3
            })
            .greyscale()
            .normalize()
            .sharpen()
            .png({ quality: 100 })
            .toFile(outputPath);

        return outputPath;
    } catch (error) {
        console.log('⚠️ Preprocessing échoué, utilisation image originale');
        return imagePath;
    }
}

// Extraction PDF
async function extractTextFromPDF(pdfPath) {
    console.log('📄 Extraction PDF...');

    try {
        const dataBuffer = await fs.readFile(pdfPath);
        const data = await pdfParse(dataBuffer);

        let text = data.text;

        // Si le PDF ne contient pas de texte (PDF scanné), utiliser OCR
        if (!text || text.trim().length < 50) {
            console.log('📄 PDF scanné détecté, utilisation OCR...');
            // Convertir PDF en images et faire OCR
            text = await ocrPDFPages(pdfPath);
        }

        console.log(`✅ PDF traité: ${text.length} caractères extraits`);
        return text;

    } catch (error) {
        console.error('❌ Erreur extraction PDF:', error);
        throw error;
    }
}

// OCR sur pages PDF scannées
async function ocrPDFPages(pdfPath) {
    // Pour l'instant, retourner un message d'info
    // En production, on utiliserait pdf2pic + OCR
    return "PDF scanné détecté. Pour une extraction complète, veuillez convertir le PDF en images et les uploader séparément.";
}

// Extraction Word
async function extractTextFromWord(wordPath) {
    console.log('📝 Extraction Word...');

    try {
        const result = await mammoth.extractRawText({ path: wordPath });
        const text = result.value;

        console.log(`✅ Word traité: ${text.length} caractères extraits`);
        return text;

    } catch (error) {
        console.error('❌ Erreur extraction Word:', error);
        throw error;
    }
}

// Extraction texte simple
async function extractTextFromTxt(txtPath) {
    console.log('📄 Lecture fichier texte...');

    try {
        const text = await fs.readFile(txtPath, 'utf-8');
        console.log(`✅ Texte lu: ${text.length} caractères`);
        return text;

    } catch (error) {
        console.error('❌ Erreur lecture texte:', error);
        throw error;
    }
}

// WebSocket pour temps réel
wss.on('connection', (ws) => {
    const clientId = Date.now().toString();
    clients.set(clientId, ws);
    
    console.log(`🔌 Client connecté: ${clientId}`);
    
    ws.send(JSON.stringify({
        type: 'connected',
        clientId,
        message: 'Connexion WebSocket établie'
    }));
    
    ws.on('message', (message) => {
        try {
            const data = JSON.parse(message);
            handleWebSocketMessage(clientId, data);
        } catch (error) {
            console.error('Erreur WebSocket:', error);
        }
    });
    
    ws.on('close', () => {
        clients.delete(clientId);
        console.log(`🔌 Client déconnecté: ${clientId}`);
    });
});

// Diffuser un événement
function broadcast(event) {
    const message = JSON.stringify(event);
    clients.forEach((ws) => {
        if (ws.readyState === WebSocket.OPEN) {
            ws.send(message);
        }
    });
}

// NOUVEAU SYSTÈME - LLM AGENT AUTONOME
const researchContexts = new Map(); // Stockage des contextes de recherche

// Gérer les messages WebSocket
async function handleWebSocketMessage(clientId, data) {
    console.log(`📨 Message de ${clientId}:`, data.type);

    if (data.type === 'message') {
        const message = data.content;
        const chatId = data.chatId || `chat_${Date.now()}`;
        console.log(`💬 Nouveau message: "${message}"`);

        // Sauvegarder le message utilisateur
        saveChatMessage(chatId, 'user', message);

        // Démarrer le système d'agent LLM autonome
        await startLLMAgentResearch(clientId, chatId, message);
    }

    if (data.type === 'interactive_response') {
        console.log(`🎯 Réponse interactive: ${data.responseType}`);
        await handleInteractiveResponse(clientId, data);
    }
}

// SYSTÈME AGENT LLM AUTONOME
async function startLLMAgentResearch(clientId, chatId, userMessage) {
    const researchId = `research_${Date.now()}`;

    // L'animation démarrera quand l'agent prendra sa première décision

    // Créer ou récupérer le contexte de recherche
    let context = researchContexts.get(chatId);

    if (!context) {
        // Nouveau contexte STRUCTURÉ
        context = {
            id: researchId,
            chatId: chatId,
            originalQuery: userMessage,

            // CONTEXTE STRUCTURÉ PAR PHASES
            phaseReports: [], // Rapports structurés de chaque phase
            consolidatedKnowledge: {}, // Connaissances organisées par domaine

            // MÉTRIQUES ET SUIVI
            currentTokens: 0,
            maxTokens: 200000,
            phases: [],
            sources: [],

            // ÉTAT ET MÉTADONNÉES
            status: 'active',
            startTime: Date.now(),
            clientId: clientId,

            // HISTORIQUE CONVERSATIONNEL
            conversationHistory: []
        };

        researchContexts.set(chatId, context);

        // PAS de message initial automatique - on attend que l'utilisateur écrive
    } else {
        // Contexte existant - traiter le nouveau message
        context.originalQuery = userMessage;
        context.status = 'active'; // Réactiver le contexte
        context.clientId = clientId; // Mettre à jour le client

        // Ajouter le message à l'historique de conversation
        if (!context.conversationHistory) {
            context.conversationHistory = [];
        }
        context.conversationHistory.push({
            role: 'user',
            content: userMessage,
            timestamp: Date.now()
        });
    }

    // Démarrer le cycle d'agent autonome
    await runLLMAgentCycle(context);
}

// CYCLE PRINCIPAL DE L'AGENT LLM AUTONOME
async function runLLMAgentCycle(context) {
    try {
        let cycleCount = 0;
        const MAX_CYCLES = 10; // Limite de sécurité
        const startTime = Date.now();
        const MAX_TIME = 5 * 60 * 1000; // 5 minutes max

        while (context.status === 'active' && cycleCount < MAX_CYCLES) {
            // Vérifier le timeout
            if (Date.now() - startTime > MAX_TIME) {
                console.log('⏰ Timeout atteint, arrêt forcé');
                await finalizeResearch(context, 'Timeout atteint après 5 minutes');
                break;
            }

            cycleCount++;
            console.log(`🔄 Cycle ${cycleCount}/${MAX_CYCLES}`);

            // 1. L'agent LLM décide de la prochaine action
            console.log(`🤖 Demande de décision à l'agent...`);

            // L'agent prend sa décision silencieusement

            const agentDecision = await getAgentDecision(context);
            console.log(`🎯 Décision agent:`, agentDecision);

            if (agentDecision.action === 'STOP') {
                console.log(`🛑 Agent décide d'arrêter: ${agentDecision.reason}`);
                await finalizeResearch(context, agentDecision.reason);
                break;
            }

            if (agentDecision.action === 'SEARCH') {
                console.log(`🔍 Agent lance recherche: ${agentDecision.searchQuery}`);
                // Démarrer l'animation de recherche existante
                const client = clients.get(context.clientId);
                if (client) {
                    client.send(JSON.stringify({
                        type: 'research_started',
                        title: 'Recherche en cours...',
                        query: agentDecision.searchQuery
                    }));
                }
                await executeAgentSearch(context, agentDecision);
            }

            if (agentDecision.action === 'MULTI_SEARCH') {
                console.log(`🔍🔍 Agent lance recherches multiples: ${agentDecision.searchQueries?.length || 0} requêtes`);
                // Démarrer l'animation de recherche existante
                const client = clients.get(context.clientId);
                if (client) {
                    client.send(JSON.stringify({
                        type: 'research_started',
                        title: 'Recherche en cours...',
                        queries: agentDecision.searchQueries
                    }));
                }
                await executeMultiSearch(context, agentDecision);
            }

            if (agentDecision.action === 'DEEP_SEARCH') {
                console.log(`🔍🎯 Agent lance recherche approfondie: ${agentDecision.searchQuery}`);
                await executeDeepSearch(context, agentDecision);
            }

            // Actions simplifiées - plus de choix complexes

            if (agentDecision.action === 'ASK_USER') {
                // L'agent veut demander à l'utilisateur
                await askUserForGuidance(context, agentDecision);
                break; // Attendre la réponse utilisateur
            }

            // Vérifier les limites de tokens
            if (context.currentTokens > context.maxTokens * 0.9) {
                await compressContext(context);
            }
        }

        // Si on sort de la boucle sans STOP, forcer l'arrêt
        if (cycleCount >= MAX_CYCLES) {
            console.log(`⚠️ Limite de cycles atteinte (${MAX_CYCLES}), arrêt forcé`);
            await finalizeResearch(context, `Limite de ${MAX_CYCLES} cycles atteinte`);
        }

    } catch (error) {
        console.error('❌ Erreur cycle agent:', error);
        context.status = 'error';

        // Arrêter l'animation
        clients.get(context.clientId)?.send(JSON.stringify({
            type: 'stop_research_animation'
        }));

        clients.get(context.clientId)?.send(JSON.stringify({
            type: 'error',
            message: 'Erreur dans le cycle de recherche'
        }));
    }
}

// L'AGENT LLM DÉCIDE DE LA PROCHAINE ACTION
async function getAgentDecision(context) {
    const prompt = `Tu es un assistant de recherche intelligent. Analyse la demande et sois direct et efficace.

CONTEXTE STRUCTURÉ ACTUEL:
- Requête originale: "${context.originalQuery}"
- Historique conversation: ${context.conversationHistory ? context.conversationHistory.map(msg => `${msg.role}: ${msg.content}`).join('\n') : "Aucun"}
- Tokens utilisés: ${context.currentTokens}/${context.maxTokens}
- Phases complétées: ${context.phases.length}
- Sources collectées: ${context.sources.length}

DOMAINES COUVERTS:
${Object.keys(context.consolidatedKnowledge).map(domain => {
    const knowledge = context.consolidatedKnowledge[domain];
    return `• ${domain}: ${knowledge.keyFindings.length} découvertes, score ${knowledge.qualityScore}/100`;
}).join('\n')}

DERNIÈRES DÉCOUVERTES:
${context.phaseReports.slice(-2).map(report =>
    `• ${report.domain}: ${report.keyFindings.slice(0, 2).join(', ')}`
).join('\n')}

ACTIONS POSSIBLES:
1. SEARCH - Faire une recherche spécifique
2. MULTI_SEARCH - Faire plusieurs recherches en parallèle (pour sujets complexes)
3. DEEP_SEARCH - Approfondir un aspect spécifique avec recherches ciblées
4. ASK_CHOICES - Proposer des choix à l'utilisateur (interface cliquable)
5. ASK_PRECISION - Demander des précisions spécifiques
6. OFFER_DEEPENING - Proposer d'approfondir certains aspects
7. ASK_USER - Demander des précisions libres à l'utilisateur
8. STOP - Arrêter et générer le rapport final

INSTRUCTIONS SIMPLES:
- Pour BALI ou destinations claires: COMMENCE DIRECTEMENT par MULTI_SEARCH (vols + hôtels + activités)
- Pour demandes vagues: Une seule question courte et naturelle
- Style naturel: Écris comme un humain, pas comme un robot
- Questions UNIQUEMENT si vraiment nécessaire (budget flou, dates manquantes)
- PHASES MINIMUM:
  * Voyage clair (Bali): 4-6 recherches directes
  * Voyage vague: 1 question puis 4-6 recherches
  * Question simple: 1-2 recherches
- STOP quand tu as assez d'infos pour un bon rapport

IMPORTANT:
- Sois PROACTIF, ne pose pas de questions inutiles
- Pour ASK_USER, écris une question naturelle comme si tu étais un assistant humain
- Utilise l'historique de conversation pour comprendre le contexte

⚠️ CRITIQUE: Réponds UNIQUEMENT en JSON valide, aucun texte avant ou après. Commence directement par {
{
    "action": "SEARCH|MULTI_SEARCH|ASK_USER|STOP",
    "reason": "Courte explication",
    "searchQuery": "Si SEARCH",
    "searchQueries": ["vols Paris Bali", "hôtels Bali", "activités Bali"] (si MULTI_SEARCH),
    "userQuestion": "Si ASK_USER: question courte et naturelle"
}`;

    try {
        const response = await callLLM(prompt);

        // Nettoyer la réponse (supprimer les backticks markdown)
        let cleanResponse = response.trim();
        if (cleanResponse.startsWith('```json')) {
            cleanResponse = cleanResponse.replace(/```json\s*/, '').replace(/```\s*$/, '');
        }
        if (cleanResponse.startsWith('```')) {
            cleanResponse = cleanResponse.replace(/```\s*/, '').replace(/```\s*$/, '');
        }

        return JSON.parse(cleanResponse);
    } catch (error) {
        console.error('Erreur décision agent:', error);
        // Ne pas référencer 'response' qui peut ne pas exister
        return {
            action: 'STOP',
            reason: 'Erreur dans la prise de décision',
            confidence: 1
        };
    }
}

// EXÉCUTER UNE RECHERCHE DEMANDÉE PAR L'AGENT
async function executeAgentSearch(context, decision) {
    try {
        // DÉMARRER L'ANIMATION SEULEMENT MAINTENANT (vraie recherche internet)
        if (!context.animationStarted) {
            clients.get(context.clientId)?.send(JSON.stringify({
                type: 'start_research_animation'
            }));
            context.animationStarted = true;
        }

        // Envoyer étape de recherche
        clients.get(context.clientId)?.send(JSON.stringify({
            type: 'add_step',
            icon: '🔍',
            text: `Recherche: "${decision.searchQuery}"`,
            status: 'active'
        }));

        // Effectuer la recherche
        const sources = await searchSearXNG(decision.searchQuery);

        // L'agent filtre et traite les résultats
        const processedResults = await processSearchResults(context, sources, decision.searchQuery);

        // GÉNÉRER RAPPORT DE PHASE STRUCTURÉ
        const phaseReport = await generatePhaseReport(context, decision.searchQuery, sources, processedResults);

        // Ajouter au contexte STRUCTURÉ
        context.sources.push(...sources);
        context.phaseReports.push(phaseReport);
        context.currentTokens += estimateTokens(JSON.stringify(phaseReport));

        // Consolider les connaissances par domaine
        await consolidateKnowledge(context, phaseReport);

        context.phases.push({
            type: 'search',
            query: decision.searchQuery,
            timestamp: Date.now(),
            sourcesFound: sources.length,
            reportId: phaseReport.id,
            keyFindings: phaseReport.keyFindings.slice(0, 3) // Top 3 pour suivi
        });

        // Compléter l'étape de recherche et ajouter l'analyse
        clients.get(context.clientId)?.send(JSON.stringify({
            type: 'complete_step'
        }));

        clients.get(context.clientId)?.send(JSON.stringify({
            type: 'add_step',
            icon: '📊',
            text: `Analyse de ${sources.length} sources trouvées`,
            status: 'active'
        }));

        // Envoyer les sources trouvées pour affichage DÉTAILLÉ
        clients.get(context.clientId)?.send(JSON.stringify({
            type: 'sources_found',
            query: decision.searchQuery, // Ajouter la requête pour contexte
            sources: sources.slice(0, 8).map((source, index) => ({ // Plus de sources
                id: index + 1,
                title: source.title || `Source ${index + 1}`,
                url: source.url || '#',
                snippet: source.snippet || source.content?.substring(0, 150) + '...' || 'Contenu non disponible',
                engine: source.engine || 'web',
                score: source.score || Math.random().toFixed(2)
            }))
        }));

    } catch (error) {
        console.error('Erreur recherche agent:', error);
    }
}

// EXÉCUTER PLUSIEURS RECHERCHES EN PARALLÈLE
async function executeMultiSearch(context, decision) {
    try {
        if (!context.animationStarted) {
            clients.get(context.clientId)?.send(JSON.stringify({
                type: 'start_research_animation'
            }));
            context.animationStarted = true;
        }

        const queries = decision.searchQueries || [];
        console.log(`🔍🔍 Lancement de ${queries.length} recherches parallèles`);

        // Lancer toutes les recherches en parallèle avec gestion d'erreur
        const searchPromises = queries.map(async (query, index) => {
            try {
                clients.get(context.clientId)?.send(JSON.stringify({
                    type: 'add_step',
                    icon: '🔍',
                    text: `Recherche ${index + 1}/${queries.length}: "${query}"`,
                    status: 'active'
                }));

                const sources = await searchSearXNG(query);
                const processedResults = await processSearchResults(context, sources, query);

                clients.get(context.clientId)?.send(JSON.stringify({
                    type: 'complete_step'
                }));

                return { query, sources: sources || [], processedResults: processedResults || {} };
            } catch (error) {
                console.error(`❌ Erreur recherche "${query}":`, error);
                clients.get(context.clientId)?.send(JSON.stringify({
                    type: 'complete_step'
                }));
                return { query, sources: [], processedResults: { summary: 'Erreur de recherche', keyFindings: [] } };
            }
        });

        // Attendre toutes les recherches avec gestion d'erreur
        const results = await Promise.allSettled(searchPromises);

        // Consolider les résultats (seulement ceux qui ont réussi)
        let totalSources = 0;
        let successfulResults = 0;

        for (const result of results) {
            if (result.status === 'fulfilled' && result.value) {
                const searchResult = result.value;
                if (searchResult.sources && Array.isArray(searchResult.sources)) {
                    context.sources.push(...searchResult.sources);
                    totalSources += searchResult.sources.length;
                }
                if (searchResult.processedResults && searchResult.processedResults.summary) {
                    context.currentTokens += estimateTokens(searchResult.processedResults.summary);
                }
                successfulResults++;
            } else {
                console.error('❌ Échec recherche:', result.reason);
            }
        }

        context.phases.push({
            type: 'multi_search',
            queries: queries,
            timestamp: Date.now(),
            sourcesFound: totalSources,
            successfulSearches: successfulResults,
            totalSearches: queries.length,
            summary: `Recherches multiples: ${successfulResults}/${queries.length} réussies, ${totalSources} sources trouvées`
        });

        clients.get(context.clientId)?.send(JSON.stringify({
            type: 'add_step',
            icon: '📊',
            text: `Analyse de ${totalSources} sources trouvées`,
            status: 'active'
        }));

        clients.get(context.clientId)?.send(JSON.stringify({
            type: 'complete_step'
        }));

    } catch (error) {
        console.error('Erreur multi-recherche:', error);
    }
}

// RECHERCHE APPROFONDIE AVEC SOUS-REQUÊTES
async function executeDeepSearch(context, decision) {
    try {
        if (!context.animationStarted) {
            clients.get(context.clientId)?.send(JSON.stringify({
                type: 'start_research_animation'
            }));
            context.animationStarted = true;
        }

        const mainQuery = decision.searchQuery;
        const subQueries = decision.subQueries || [];

        console.log(`🔍🎯 Recherche approfondie: ${mainQuery} + ${subQueries.length} sous-requêtes`);

        // Recherche principale
        await executeAgentSearch(context, { searchQuery: mainQuery });

        // Recherches de suivi
        for (const subQuery of subQueries) {
            await executeAgentSearch(context, { searchQuery: subQuery });
        }

        context.phases.push({
            type: 'deep_search',
            mainQuery: mainQuery,
            subQueries: subQueries,
            timestamp: Date.now(),
            summary: `Recherche approfondie: 1 requête principale + ${subQueries.length} sous-requêtes`
        });

    } catch (error) {
        console.error('Erreur recherche approfondie:', error);
    }
}

// GÉNÉRER UN RAPPORT STRUCTURÉ POUR CHAQUE PHASE
async function generatePhaseReport(context, query, sources, processedResults) {
    const prompt = `Tu es un agent spécialisé dans l'analyse et la structuration d'informations.

MISSION: Analyser les résultats de recherche et créer un rapport structuré pour cette phase.

REQUÊTE DE RECHERCHE: "${query}"
CONTEXTE GLOBAL: "${context.originalQuery}"

RÉSULTATS À ANALYSER:
${processedResults.summary}

SOURCES TROUVÉES: ${sources.length} sources

INSTRUCTIONS:
Génère un rapport JSON structuré avec:

{
    "id": "phase_${Date.now()}",
    "query": "${query}",
    "timestamp": ${Date.now()},
    "domain": "Domaine principal (ex: transport, hébergement, activités)",
    "keyFindings": [
        "Découverte importante 1",
        "Découverte importante 2",
        "Découverte importante 3"
    ],
    "structuredData": {
        "prices": {"min": 0, "max": 0, "average": 0, "currency": "EUR"},
        "locations": ["lieu1", "lieu2"],
        "timeframes": ["période1", "période2"],
        "recommendations": ["conseil1", "conseil2"]
    },
    "qualityScore": 85,
    "coverage": "Aspects couverts par cette recherche",
    "gaps": "Aspects manquants nécessitant approfondissement",
    "nextSteps": ["Action recommandée 1", "Action recommandée 2"]
}

IMPORTANT: Réponds UNIQUEMENT avec le JSON, rien d'autre.`;

    try {
        const response = await callLLM(prompt);
        const report = JSON.parse(response);
        return report;
    } catch (error) {
        console.error('Erreur génération rapport phase:', error);
        // Rapport de fallback
        return {
            id: `phase_${Date.now()}`,
            query: query,
            timestamp: Date.now(),
            domain: "général",
            keyFindings: ["Données collectées", "Analyse en cours", "Informations disponibles"],
            structuredData: {},
            qualityScore: 50,
            coverage: "Recherche effectuée",
            gaps: "Analyse détaillée nécessaire",
            nextSteps: ["Continuer la recherche"]
        };
    }
}

// CONSOLIDER LES CONNAISSANCES PAR DOMAINE
async function consolidateKnowledge(context, phaseReport) {
    const domain = phaseReport.domain;

    if (!context.consolidatedKnowledge[domain]) {
        context.consolidatedKnowledge[domain] = {
            phases: [],
            keyFindings: [],
            structuredData: {},
            qualityScore: 0,
            lastUpdated: Date.now()
        };
    }

    const domainKnowledge = context.consolidatedKnowledge[domain];

    // Ajouter cette phase
    domainKnowledge.phases.push(phaseReport.id);

    // Fusionner les découvertes clés (éviter doublons)
    phaseReport.keyFindings.forEach(finding => {
        if (!domainKnowledge.keyFindings.includes(finding)) {
            domainKnowledge.keyFindings.push(finding);
        }
    });

    // Fusionner les données structurées
    if (phaseReport.structuredData) {
        Object.keys(phaseReport.structuredData).forEach(key => {
            if (!domainKnowledge.structuredData[key]) {
                domainKnowledge.structuredData[key] = phaseReport.structuredData[key];
            } else {
                // Logique de fusion intelligente selon le type
                if (Array.isArray(domainKnowledge.structuredData[key])) {
                    // Fusionner les tableaux
                    const newItems = phaseReport.structuredData[key].filter(
                        item => !domainKnowledge.structuredData[key].includes(item)
                    );
                    domainKnowledge.structuredData[key].push(...newItems);
                }
            }
        });
    }

    // Mettre à jour le score de qualité (moyenne pondérée)
    const phaseCount = domainKnowledge.phases.length;
    domainKnowledge.qualityScore = Math.round(
        (domainKnowledge.qualityScore * (phaseCount - 1) + phaseReport.qualityScore) / phaseCount
    );

    domainKnowledge.lastUpdated = Date.now();

    console.log(`📊 Domaine "${domain}" consolidé: ${domainKnowledge.keyFindings.length} découvertes, score ${domainKnowledge.qualityScore}`);
}

// L'AGENT TRAITE LES RÉSULTATS DE RECHERCHE
async function processSearchResults(context, sources, query) {
    const prompt = `Tu es un agent de recherche intelligent. Analyse ces résultats de recherche et extrait seulement les informations pertinentes pour enrichir la base de connaissances.

REQUÊTE: "${query}"
CONTEXTE EXISTANT: "Recherche en cours..."

RÉSULTATS DE RECHERCHE:
${(sources || []).slice(0, 10).map(s => `- ${s.title || 'Titre manquant'}: ${s.content || s.snippet || 'Contenu non disponible'}`).join('\n')}

INSTRUCTIONS CRITIQUES:
1. Filtre et garde seulement les informations pertinentes et nouvelles
2. Ignore les doublons avec le contexte existant
3. Synthétise en format structuré
4. Estime les tokens utilisés

⚠️ IMPORTANT: Réponds UNIQUEMENT en JSON valide, aucun texte avant ou après. Commence directement par {
{
    "summary": "Résumé structuré des nouvelles informations pertinentes",
    "keyFindings": ["Point clé 1", "Point clé 2", ...],
    "estimatedTokens": nombre_de_tokens_estimé,
    "relevanceScore": score_1_à_10
}`;

    try {
        const response = await callLLM(prompt);

        // Nettoyer la réponse JSON
        let cleanResponse = response.trim();
        if (cleanResponse.startsWith('```json')) {
            cleanResponse = cleanResponse.replace(/```json\s*/, '').replace(/```\s*$/, '');
        }
        if (cleanResponse.startsWith('```')) {
            cleanResponse = cleanResponse.replace(/```\s*/, '').replace(/```\s*$/, '');
        }

        return JSON.parse(cleanResponse);
    } catch (error) {
        console.error('Erreur traitement résultats:', error);
        // Fallback sécurisé
        const safeSources = (sources || []).filter(s => s && s.title);
        return {
            summary: safeSources.slice(0, 3).map(s => s.title || 'Source sans titre').join(', ') || 'Aucun résultat traitable',
            keyFindings: [],
            estimatedTokens: 1000,
            relevanceScore: 5
        };
    }
}

// Routes API

// Page d'accueil - Interface propre style Grok
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'clean-chat.html'));
});

// Route pour récupérer l'historique des conversations
app.get('/api/chat-history', (req, res) => {
    res.json(getChatHistory());
});

// Route pour charger une conversation spécifique
app.get('/api/chat/:chatId', (req, res) => {
    const chatId = req.params.chatId;
    console.log(`📖 Demande de chargement conversation: ${chatId}`);

    if (chatHistory.has(chatId)) {
        const conversation = chatHistory.get(chatId);
        console.log(`✅ Conversation trouvée: ${conversation.messages.length} messages`);
        res.json({
            id: chatId,
            messages: conversation.messages,
            createdAt: conversation.createdAt,
            updatedAt: conversation.updatedAt
        });
    } else {
        console.log(`❌ Conversation non trouvée: ${chatId}`);
        res.status(404).json({ error: 'Conversation non trouvée' });
    }
});

// Route pour créer une nouvelle conversation
app.post('/api/chat/create', (req, res) => {
    const { chatId } = req.body;
    if (!chatId) {
        return res.status(400).json({ error: 'chatId requis' });
    }

    console.log(`🆕 Création conversation: ${chatId}`);
    // Ne pas envoyer de message automatique

    res.json({ success: true, chatId });
});

// DEMANDER DES PRÉCISIONS À L'UTILISATEUR
async function askUserForGuidance(context, decision) {
    // Message naturel sans détails techniques
    const message = decision.userQuestion;

    saveChatMessage(context.chatId, 'assistant', message);

    // Envoyer avec effet de streaming
    await sendMessageWithStreaming(context.clientId, message);

    // Marquer en attente de réponse utilisateur
    context.status = 'waiting_user';
}

// PROPOSER DES CHOIX CLIQUABLES À L'UTILISATEUR
async function askUserChoices(context, decision) {
    const choicesMessage = {
        type: 'interactive_choices',
        question: decision.reason,
        choices: decision.choices || [],
        context: context.id
    };

    clients.get(context.clientId)?.send(JSON.stringify(choicesMessage));
    context.status = 'waiting_user';
}

// DEMANDER UNE PRÉCISION SPÉCIFIQUE
async function askUserPrecision(context, decision) {
    const precisionMessage = {
        type: 'precision_request',
        question: `Pour vous donner les meilleures recommandations, j'ai besoin de précisions sur : **${decision.precisionNeeded}**`,
        placeholder: `Précisez ${decision.precisionNeeded}...`,
        context: context.id
    };

    clients.get(context.clientId)?.send(JSON.stringify(precisionMessage));
    context.status = 'waiting_user';
}

// PROPOSER D'APPROFONDIR CERTAINS ASPECTS
async function offerDeepening(context, decision) {
    const deepeningMessage = {
        type: 'deepening_offer',
        question: "Je peux approfondir ces aspects pour vous donner encore plus de détails :",
        options: decision.deepeningOptions || [],
        context: context.id
    };

    clients.get(context.clientId)?.send(JSON.stringify(deepeningMessage));
    context.status = 'waiting_user';
}

// GÉRER LES RÉPONSES INTERACTIVES DE L'UTILISATEUR
async function handleInteractiveResponse(clientId, data) {
    const contextId = data.data.contextId;

    // Chercher le contexte par clientId si contextId ne fonctionne pas
    let context = researchContexts.get(contextId);
    if (!context) {
        // Fallback: chercher par clientId
        for (const [key, ctx] of researchContexts.entries()) {
            if (ctx.clientId === clientId) {
                context = ctx;
                console.log(`🔄 Contexte trouvé via clientId: ${key}`);
                break;
            }
        }
    }

    if (!context) {
        console.error('❌ Aucun contexte trouvé pour:', { contextId, clientId });
        return;
    }

    let userResponse = '';

    switch (data.responseType) {
        case 'choice_selected':
            userResponse = `Choix sélectionné: ${data.data.choiceId}`;
            break;

        case 'precision_provided':
            userResponse = `Précision fournie: ${data.data.precision}`;
            break;

        case 'deepening_selected':
            if (data.data.aspects.length > 0) {
                userResponse = `Approfondissement demandé pour: ${data.data.aspects.join(', ')}`;
            } else {
                userResponse = 'Aucun approfondissement sélectionné';
            }
            break;

        case 'deepening_skipped':
            userResponse = 'Approfondissement ignoré, continuer avec les informations actuelles';
            break;
    }

    // Sauvegarder la réponse utilisateur
    saveChatMessage(context.chatId, 'user', userResponse);

    // Ajouter la réponse au contexte
    context.conversationHistory = context.conversationHistory || [];
    context.conversationHistory.push({
        role: 'user',
        content: userResponse
    });

    // Reprendre le cycle de recherche
    context.status = 'active';
    await runLLMAgentCycle(context);
}

// FINALISER LA RECHERCHE ET GÉNÉRER LE RAPPORT
async function finalizeResearch(context, reason) {
    try {
        const finalMessage = `🎯 **Recherche terminée**\n\n*Raison*: ${reason}\n\nGénération du rapport final en cours...`;

        clients.get(context.clientId)?.send(JSON.stringify({
            type: 'message',
            content: finalMessage
        }));

        // Générer le rapport final avec les connaissances consolidées
        let finalReport;
        if (context.phaseReports && context.phaseReports.length > 0) {
            finalReport = await generateAgentFinalReport(context);
        } else {
            finalReport = `❌ **Recherche interrompue**\n\n*Raison*: ${reason}\n\nAucune donnée n'a pu être collectée. Veuillez reformuler votre demande ou vérifier la connectivité.`;
        }

        saveChatMessage(context.chatId, 'assistant', finalReport);
        clients.get(context.clientId)?.send(JSON.stringify({
            type: 'message',
            content: finalReport
        }));

        context.status = 'completed';

        // Arrêter l'animation de recherche
        clients.get(context.clientId)?.send(JSON.stringify({
            type: 'stop_research_animation',
            reason: reason
        }));

    } catch (error) {
        console.error('Erreur finalisation:', error);
    }
}

// AGENT SPÉCIALISÉ POUR RAPPORT FINAL STRUCTURÉ
async function generateAgentFinalReport(context) {
    console.log('🎯 Agent rapport final: Analyse des connaissances consolidées...');

    const prompt = `Tu es un agent spécialisé dans la rédaction de rapports finaux complets.

MISSION: Créer un rapport final exhaustif basé sur toutes les recherches structurées.

REQUÊTE ORIGINALE: "${context.originalQuery}"

CONNAISSANCES CONSOLIDÉES PAR DOMAINE:
${JSON.stringify(context.consolidatedKnowledge, null, 2)}

RAPPORTS DE PHASES:
${context.phaseReports.map(report => `
Phase ${report.id}:
- Domaine: ${report.domain}
- Découvertes: ${report.keyFindings.join(', ')}
- Score qualité: ${report.qualityScore}/100
- Lacunes: ${report.gaps}
`).join('\n')}

STATISTIQUES:
- Phases de recherche: ${context.phases.length}
- Sources consultées: ${context.sources.length}
- Tokens utilisés: ${context.currentTokens}/${context.maxTokens}

INSTRUCTIONS SPÉCIALES POUR VOYAGE:
Pour un itinéraire de voyage, génère un rapport ULTRA-DÉTAILLÉ avec:

# 🌴✨ Itinéraire Complet : [Destination]

## 🎯 Résumé Exécutif
- Durée recommandée
- Budget total estimé
- Meilleure période
- Points forts du voyage

## ✈️ Transport & Vols
- Compagnies recommandées avec prix
- Horaires optimaux
- Escales et durées
- Conseils réservation

## 🏨 Hébergements par Zone
- **Zone 1** : Hôtels 3-4-5 étoiles avec prix, avantages, localisation
- **Zone 2** : Idem
- **Zone 3** : Idem

## 🎭 Activités & Excursions
- **Jour 1-3** : Programme détaillé avec horaires, prix, durées
- **Jour 4-6** : Idem
- **Jour 7+** : Idem

## 🚗 Transports Locaux
- Options de déplacement
- Prix et conseils
- Applications utiles

## 💰 Budget Détaillé
- Coûts par catégorie
- Alternatives économiques/luxe
- Conseils économies

## 📅 Planning Jour par Jour
- Itinéraire optimisé
- Temps de trajet
- Réservations nécessaires

## 🎒 Conseils Pratiques
- Visa, vaccins, assurance
- Que emporter
- Applications utiles
- Numéros d'urgence

Format: Markdown avec émojis, tableaux, et sections très détaillées.`;

    try {
        return await callLLM(prompt);
    } catch (error) {
        console.error('Erreur rapport final:', error);
        return '❌ Erreur lors de la génération du rapport final.';
    }
}

// Route pour récupérer une conversation spécifique
app.get('/api/chat/:chatId', (req, res) => {
    const chat = getChat(req.params.chatId);
    if (chat) {
        res.json(chat);
    } else {
        res.status(404).json({ error: 'Conversation non trouvée' });
    }
});

// Route de test pour l'agent LLM
app.post('/api/test-agent', async (req, res) => {
    try {
        const { message } = req.body;
        const testClientId = 'test_client';
        const testChatId = `test_chat_${Date.now()}`;

        console.log(`🧪 Test agent avec: "${message}"`);

        // Simuler un client WebSocket pour le test
        const mockClient = {
            send: (data) => {
                const parsed = JSON.parse(data);
                console.log(`📤 Message envoyé: ${parsed.content?.slice(0, 100)}...`);
            }
        };
        clients.set(testClientId, mockClient);

        // Démarrer le système d'agent
        await startLLMAgentResearch(testClientId, testChatId, message);

        res.json({
            success: true,
            message: 'Agent démarré avec succès',
            chatId: testChatId
        });

    } catch (error) {
        console.error('❌ Erreur test agent:', error);
        res.status(500).json({ error: error.message });
    }
});

// Interface moderne (alternative)
app.get('/modern', (req, res) => {
    res.sendFile(path.join(__dirname, 'modern-chat.html'));
});

// Interface classique (fallback)
app.get('/old', (req, res) => {
    res.sendFile(path.join(__dirname, 'chat.html'));
});

// ==================== ROUTES UPLOAD & OCR ====================

// Upload et traitement de fichiers
app.post('/upload', upload.single('file'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: 'Aucun fichier uploadé' });
        }

        const { filename, originalname, mimetype, size, path: filePath } = req.file;

        console.log(`📁 Fichier reçu: ${originalname} (${(size/1024/1024).toFixed(2)}MB)`);

        // Extraire le texte du fichier
        const extractedText = await extractTextFromFile(filePath, mimetype, originalname);

        // Préparer la réponse
        const response = {
            success: true,
            file: {
                name: originalname,
                size: size,
                type: mimetype,
                url: `/uploads/${filename}`
            },
            extractedText: extractedText,
            textLength: extractedText.length,
            message: `✅ Fichier traité avec succès ! ${extractedText.length} caractères extraits.`
        };

        // Nettoyer le fichier temporaire après extraction
        setTimeout(async () => {
            try {
                await fs.unlink(filePath);
                // Nettoyer aussi le fichier preprocessé s'il existe
                const processedPath = filePath.replace(/\.[^/.]+$/, '_processed.png');
                try {
                    await fs.unlink(processedPath);
                } catch (e) {
                    // Fichier preprocessé n'existe pas, c'est normal
                }
            } catch (error) {
                console.log('⚠️ Impossible de supprimer le fichier temporaire:', error.message);
            }
        }, 5000); // Attendre 5 secondes avant de nettoyer

        res.json(response);

    } catch (error) {
        console.error('❌ Erreur upload:', error);
        res.status(500).json({
            error: 'Erreur lors du traitement du fichier',
            details: error.message
        });
    }
});

// Upload multiple fichiers
app.post('/upload-multiple', upload.array('files', 10), async (req, res) => {
    try {
        if (!req.files || req.files.length === 0) {
            return res.status(400).json({ error: 'Aucun fichier uploadé' });
        }

        console.log(`📁 ${req.files.length} fichiers reçus`);

        const results = [];

        for (const file of req.files) {
            try {
                const extractedText = await extractTextFromFile(file.path, file.mimetype, file.originalname);

                results.push({
                    file: {
                        name: file.originalname,
                        size: file.size,
                        type: file.mimetype,
                        url: `/uploads/${file.filename}`
                    },
                    extractedText: extractedText,
                    textLength: extractedText.length,
                    success: true
                });

                // Nettoyer le fichier temporaire
                setTimeout(async () => {
                    try {
                        await fs.unlink(file.path);
                    } catch (error) {
                        console.log('⚠️ Impossible de supprimer le fichier temporaire:', error.message);
                    }
                }, 5000);

            } catch (error) {
                results.push({
                    file: {
                        name: file.originalname,
                        size: file.size,
                        type: file.mimetype
                    },
                    error: error.message,
                    success: false
                });
            }
        }

        const totalChars = results
            .filter(r => r.success)
            .reduce((sum, r) => sum + r.textLength, 0);

        res.json({
            success: true,
            files: results,
            totalFiles: req.files.length,
            successfulFiles: results.filter(r => r.success).length,
            totalCharacters: totalChars,
            message: `✅ ${results.filter(r => r.success).length}/${req.files.length} fichiers traités avec succès ! ${totalChars} caractères extraits au total.`
        });

    } catch (error) {
        console.error('❌ Erreur upload multiple:', error);
        res.status(500).json({
            error: 'Erreur lors du traitement des fichiers',
            details: error.message
        });
    }
});

// Health check
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        model: LLM_MODEL,
        context: '1M tokens'
    });
});

// Chat endpoint - point d'entrée principal
app.post('/api/chat', async (req, res) => {
    const { message, depth = 'standard' } = req.body;

    if (!message) {
        return res.status(400).json({
            success: false,
            error: 'Message required'
        });
    }

    console.log(`💬 Nouveau message: "${message}"`);

    try {
        // Analyser si une recherche est nécessaire
        const needsResearch = await analyzeIfResearchNeeded(message);

        if (needsResearch.shouldResearch) {
            // Démarrer une recherche
            const researchId = `research_${Date.now()}`;

            // Créer la recherche
            const research = {
                id: researchId,
                query: needsResearch.optimizedQuery || message,
                depth,
                status: 'started',
                startTime: Date.now(),
                phases: [],
                sources: [],
                context: '',
                iterations: 0,
                maxIterations: getMaxIterations(depth),
                originalMessage: message
            };

            activeResearches.set(researchId, research);

            // Démarrer le processus en arrière-plan
            startIntelligentResearch(researchId);

            res.json({
                success: true,
                response: needsResearch.initialResponse,
                researchId,
                needsResearch: true
            });

        } else {
            // Réponse directe sans recherche
            const response = await generateDirectResponse(message);

            res.json({
                success: true,
                response,
                needsResearch: false
            });
        }

    } catch (error) {
        console.error('Erreur chat:', error);
        res.status(500).json({
            success: false,
            error: 'Erreur interne du serveur'
        });
    }
});

// Recherche intelligente
app.post('/api/research', async (req, res) => {
    const { query, depth = 'standard', options = {} } = req.body;
    
    if (!query) {
        return res.status(400).json({
            success: false,
            error: 'Query required'
        });
    }
    
    const researchId = `research_${Date.now()}`;
    
    console.log(`🧠 Nouvelle recherche: "${query}" (profondeur: ${depth})`);
    
    // Créer la recherche
    const research = {
        id: researchId,
        query,
        depth,
        options,
        status: 'started',
        startTime: Date.now(),
        phases: [],
        sources: [],
        context: '',
        iterations: 0,
        maxIterations: getMaxIterations(depth)
    };
    
    activeResearches.set(researchId, research);
    
    // Démarrer le processus
    startIntelligentResearch(researchId);
    
    res.json({
        success: true,
        researchId,
        status: 'started',
        depth,
        maxIterations: research.maxIterations
    });
});

// Statut d'une recherche
app.get('/api/research/:id', (req, res) => {
    const research = activeResearches.get(req.params.id);
    
    if (!research) {
        return res.status(404).json({
            success: false,
            error: 'Recherche non trouvée'
        });
    }
    
    res.json({
        success: true,
        data: {
            id: research.id,
            query: research.query,
            status: research.status,
            progress: calculateProgress(research),
            phases: research.phases,
            sources: research.sources,
            iterations: research.iterations,
            maxIterations: research.maxIterations,
            duration: Date.now() - research.startTime
        }
    });
});

// Rapport final
app.get('/api/research/:id/report', (req, res) => {
    const research = activeResearches.get(req.params.id);
    
    if (!research || research.status !== 'completed') {
        return res.status(404).json({
            success: false,
            error: 'Rapport non disponible'
        });
    }
    
    res.json({
        success: true,
        report: research.finalReport
    });
});

// Déterminer le nombre max d'itérations selon la profondeur
function getMaxIterations(depth) {
    const depths = {
        'quick': 1,      // Recherche rapide
        'standard': 2,   // Recherche standard
        'deep': 3,       // Recherche approfondie
        'exhaustive': 5  // Recherche exhaustive
    };
    return depths[depth] || 2;
}

// Calculer le progrès
function calculateProgress(research) {
    const totalPhases = 4; // planning, searching, analyzing, synthesizing
    const currentPhase = research.phases.length;
    const iterationProgress = (research.iterations / research.maxIterations) * 100;
    const phaseProgress = (currentPhase / totalPhases) * 100;
    
    return Math.min(Math.round((iterationProgress + phaseProgress) / 2), 100);
}

// Démarrer la recherche intelligente APPROFONDIE
async function startIntelligentResearch(researchId) {
    const research = activeResearches.get(researchId);
    if (!research) return;

    try {
        broadcast({
            type: 'research_started',
            researchId,
            query: research.query,
            depth: research.depth,
            estimatedDuration: research.maxIterations * 60 // secondes
        });

        // PHASE 1: PLANIFICATION STRATÉGIQUE (30-60s)
        await executePhase(researchId, 'strategic_planning', '🎯 Planification stratégique', async () => {
            await simulateWork(15000, 30000); // 15-30s
            const strategy = await planResearchStrategy(research);
            research.currentStrategy = strategy;

            broadcast({
                type: 'phase_progress',
                researchId,
                phase: 'strategic_planning',
                progress: 100,
                details: `Stratégie définie: ${strategy.queries?.length || 3} axes de recherche identifiés`
            });

            return strategy;
        });

        // PHASE 2: COLLECTE MASSIVE DE DONNÉES (60-120s)
        await executePhase(researchId, 'data_collection', '📊 Collecte massive de données', async () => {
            const allSources = [];
            const queries = research.currentStrategy?.queries || [research.query];

            for (let i = 0; i < queries.length; i++) {
                const query = queries[i];

                broadcast({
                    type: 'phase_progress',
                    researchId,
                    phase: 'data_collection',
                    progress: Math.round((i / queries.length) * 100),
                    details: `Recherche en cours: "${query}"`
                });

                await simulateWork(10000, 20000); // 10-20s par requête
                const sources = await performSearch(research, query);
                allSources.push(...sources);

                broadcast({
                    type: 'sources_found',
                    researchId,
                    query,
                    count: sources.length,
                    totalSources: allSources.length,
                    sources: sources.slice(0, 3)
                });
            }

            research.sources = allSources;
            return allSources;
        });

        // PHASE 3: ANALYSE QUALITATIVE (45-90s)
        await executePhase(researchId, 'quality_analysis', '🔍 Analyse qualitative approfondie', async () => {
            await simulateWork(20000, 40000); // 20-40s

            broadcast({
                type: 'phase_progress',
                researchId,
                phase: 'quality_analysis',
                progress: 50,
                details: 'Évaluation de la crédibilité des sources...'
            });

            await simulateWork(15000, 25000); // 15-25s
            const analysis = await analyzeResults(research);
            research.currentAnalysis = analysis;

            broadcast({
                type: 'phase_progress',
                researchId,
                phase: 'quality_analysis',
                progress: 100,
                details: `Analyse terminée: Score qualité ${analysis.quality_score || 85}/100`
            });

            return analysis;
        });

        // PHASE 4: RECHERCHE COMPLÉMENTAIRE (optionnelle, 30-60s)
        const needsMoreData = research.sources.length < 20 || (research.currentAnalysis?.quality_score || 85) < 80;

        if (needsMoreData && research.maxIterations > 1) {
            await executePhase(researchId, 'complementary_search', '🔄 Recherche complémentaire', async () => {
                await simulateWork(15000, 30000);

                broadcast({
                    type: 'phase_progress',
                    researchId,
                    phase: 'complementary_search',
                    progress: 100,
                    details: 'Sources complémentaires collectées'
                });

                // Ajouter des sources simulées
                const additionalSources = await performSearch(research, research.query + ' détaillé');
                research.sources.push(...additionalSources);

                return additionalSources;
            });
        }

        // PHASE 5: SYNTHÈSE FINALE (60-90s)
        await executePhase(researchId, 'final_synthesis', '📝 Synthèse finale et rédaction', async () => {
            broadcast({
                type: 'phase_progress',
                researchId,
                phase: 'final_synthesis',
                progress: 25,
                details: 'Structuration des informations...'
            });

            await simulateWork(20000, 30000);

            broadcast({
                type: 'phase_progress',
                researchId,
                phase: 'final_synthesis',
                progress: 60,
                details: 'Rédaction du rapport détaillé...'
            });

            await simulateWork(25000, 35000);

            const report = await generateFinalReport(research);
            research.finalReport = report;

            broadcast({
                type: 'phase_progress',
                researchId,
                phase: 'final_synthesis',
                progress: 100,
                details: 'Rapport final généré avec succès'
            });

            return report;
        });

        research.status = 'completed';

        broadcast({
            type: 'research_completed',
            researchId,
            duration: Date.now() - research.startTime,
            totalSources: research.sources.length,
            phases: research.phases.length,
            report: research.finalReport
        });

        console.log(`✅ Recherche ${researchId} terminée - ${research.sources.length} sources analysées`);

    } catch (error) {
        console.error(`❌ Erreur recherche ${researchId}:`, error);
        research.status = 'failed';
        research.error = error.message;

        broadcast({
            type: 'research_failed',
            researchId,
            error: error.message
        });
    }
}

// Simuler du travail réaliste
async function simulateWork(minMs, maxMs) {
    const duration = minMs + Math.random() * (maxMs - minMs);
    await new Promise(resolve => setTimeout(resolve, duration));
}

// Exécuter une phase
async function executePhase(researchId, phaseName, title, executor) {
    const research = activeResearches.get(researchId);
    if (!research) return;
    
    const phase = {
        name: phaseName,
        title,
        startTime: Date.now(),
        status: 'running'
    };
    
    research.phases.push(phase);
    research.status = phaseName;
    
    broadcast({
        type: 'phase_started',
        researchId,
        phase: phaseName,
        title
    });
    
    try {
        const result = await executor();
        
        phase.status = 'completed';
        phase.endTime = Date.now();
        phase.result = result;
        
        broadcast({
            type: 'phase_completed',
            researchId,
            phase: phaseName,
            title,
            duration: phase.endTime - phase.startTime,
            result
        });
        
        return result;
        
    } catch (error) {
        phase.status = 'failed';
        phase.error = error.message;
        throw error;
    }
}

// Planifier la stratégie de recherche avec LLM
async function planResearchStrategy(research) {
    const prompt = `Tu es un expert en recherche d'information. Analyse cette requête et crée une stratégie optimale.

Requête: "${research.query}"
Profondeur: ${research.depth}
Itération: ${research.iterations}/${research.maxIterations}
Sources déjà trouvées: ${research.sources.length}

Contexte précédent: ${research.context || 'Aucun'}

Génère une stratégie JSON avec:
- queries: liste de 3-5 requêtes de recherche optimisées
- focus: domaines prioritaires à explorer
- filters: critères de qualité des sources
- next_steps: actions pour cette itération

Réponds uniquement en JSON valide.`;

    try {
        const response = await callLLM(prompt);
        return JSON.parse(response);
    } catch (error) {
        console.error('Erreur planification:', error);
        return {
            queries: [research.query],
            focus: ['general'],
            filters: ['relevance'],
            next_steps: ['search']
        };
    }
}

// Effectuer la recherche
async function performSearch(research) {
    const strategy = research.currentStrategy;
    const allSources = [];
    
    for (const query of strategy.queries || [research.query]) {
        try {
            const sources = await searchSearXNG(query);
            allSources.push(...sources);
            
            broadcast({
                type: 'sources_found',
                researchId: research.id,
                query,
                count: sources.length,
                sources: sources.slice(0, 5) // Envoyer les 5 premiers
            });
            
            // Délai entre les requêtes
            await new Promise(resolve => setTimeout(resolve, 1000));
            
        } catch (error) {
            console.error(`Erreur recherche "${query}":`, error);
        }
    }
    
    return allSources;
}

// Rechercher sur SearXNG
async function searchSearXNG(query) {
    try {
        const response = await axios.get(`${SEARXNG_URL}/search`, {
            params: {
                q: query,
                format: 'json',
                categories: 'general,news,science'
            },
            timeout: 30000 // 30 secondes pour SearXNG
        });
        
        return response.data.results?.map(result => ({
            title: result.title || 'Titre non disponible',
            url: result.url || '',
            content: result.content || result.snippet || result.description || 'Contenu non disponible',
            score: Math.random() * 0.3 + 0.7, // Score simulé
            source: 'searxng',
            timestamp: new Date().toISOString()
        })) || [];
        
    } catch (error) {
        console.error('Erreur SearXNG:', error);
        return [];
    }
}

// Analyser les résultats avec LLM
async function analyzeResults(research) {
    const recentSources = research.sources.slice(-10); // 10 dernières sources
    
    const prompt = `Analyse ces sources de recherche et évalue leur qualité:

Requête originale: "${research.query}"
Sources à analyser: ${JSON.stringify(recentSources, null, 2)}

Évalue:
1. Pertinence par rapport à la requête
2. Qualité et crédibilité des sources
3. Couverture des aspects importants
4. Lacunes identifiées

Réponds en JSON avec:
- quality_score: note sur 100
- coverage_areas: domaines couverts
- missing_areas: domaines manquants
- recommendations: recommandations pour la suite
- should_continue: true/false pour continuer la recherche`;

    try {
        const response = await callLLM(prompt);
        return JSON.parse(response);
    } catch (error) {
        console.error('Erreur analyse:', error);
        return {
            quality_score: 75,
            coverage_areas: ['general'],
            missing_areas: [],
            recommendations: ['continue'],
            should_continue: false
        };
    }
}

// Décider si continuer la recherche
async function decideContinuation(research) {
    if (research.iterations >= research.maxIterations) {
        return false;
    }
    
    const analysis = research.currentAnalysis;
    
    // Critères de décision
    const hasGoodQuality = analysis.quality_score >= 80;
    const hasEnoughSources = research.sources.length >= 20;
    const noMissingAreas = analysis.missing_areas?.length === 0;
    
    // Pour recherche exhaustive, continuer même si qualité OK
    if (research.depth === 'exhaustive') {
        return research.iterations < research.maxIterations;
    }
    
    // Arrêter si qualité suffisante et pas de lacunes
    return !(hasGoodQuality && hasEnoughSources && noMissingAreas);
}

// Optimiser le contexte
async function optimizeContext(research) {
    const prompt = `Optimise le contexte de recherche basé sur les résultats actuels:

Requête: "${research.query}"
Sources trouvées: ${research.sources.length}
Analyse actuelle: ${JSON.stringify(research.currentAnalysis)}

Génère un contexte optimisé pour la prochaine itération qui:
1. Résume les découvertes importantes
2. Identifie les lacunes à combler
3. Oriente la recherche suivante

Contexte optimisé (max 500 mots):`;

    try {
        const optimizedContext = await callLLM(prompt);
        research.context = optimizedContext;
        return optimizedContext;
    } catch (error) {
        console.error('Erreur optimisation contexte:', error);
        return research.context;
    }
}

// Générer le rapport final
async function generateFinalReport(research, clientId = null) {
    // Ajouter l'étape de génération
    if (clientId) {
        clients.get(clientId)?.send(JSON.stringify({
            type: 'complete_step'
        }));

        clients.get(clientId)?.send(JSON.stringify({
            type: 'add_step',
            icon: '✍️',
            text: 'Génération du rapport final',
            status: 'active'
        }));
    }

    const prompt = `Génère un rapport complet de recherche basé sur toutes les données collectées:

Requête originale: "${research.query}"
Profondeur: ${research.depth}
Nombre d'itérations: ${research.iterations}
Total sources: ${research.sources.length}
Contexte final: ${research.context}

Génère un rapport structuré en HTML avec:
1. Résumé exécutif
2. Méthodologie utilisée
3. Principales découvertes
4. Sources clés
5. Conclusions et recommandations

Format: HTML complet avec CSS intégré pour un rendu professionnel.`;

    try {
        const report = await callLLM(prompt);
        return report;
    } catch (error) {
        console.error('Erreur génération rapport:', error);
        return '<h1>Erreur génération rapport</h1>';
    }
}

// FONCTIONS UTILITAIRES POUR LE NOUVEAU SYSTÈME

// Estimer le nombre de tokens d'un texte
function estimateTokens(text) {
    // Estimation approximative: 1 token ≈ 4 caractères
    return Math.ceil(text.length / 4);
}

// Compresser le contexte quand il devient trop volumineux
async function compressContext(context) {
    // Avec le nouveau système structuré, on compresse les rapports de phase
    if (context.phaseReports.length > 10) {
        // Garder seulement les 8 rapports les plus récents
        const oldCount = context.phaseReports.length;
        context.phaseReports = context.phaseReports.slice(-8);

        // Recalculer les tokens
        const newTokens = context.phaseReports.reduce((total, report) =>
            total + estimateTokens(JSON.stringify(report)), 0);

        console.log(`📦 Contexte compressé: ${oldCount} → ${context.phaseReports.length} rapports, ${context.currentTokens} → ${newTokens} tokens`);
        context.currentTokens = newTokens;
    }
}

// Extraire mots-clés d'un message
function extractKeywords(message) {
    return message.split(' ')
        .filter(word => word.length > 3)
        .slice(0, 5)
        .join(' ');
}

// Envoyer un message avec effet de streaming
async function sendMessageWithStreaming(clientId, message) {
    const client = clients.get(clientId);
    if (!client) return;

    // Diviser le message en mots
    const words = message.split(' ');
    let currentMessage = '';

    for (let i = 0; i < words.length; i++) {
        currentMessage += (i > 0 ? ' ' : '') + words[i];

        client.send(JSON.stringify({
            type: 'message_stream',
            content: currentMessage,
            isComplete: i === words.length - 1
        }));

        // Délai pour simuler la frappe (plus rapide que vraie frappe)
        await new Promise(resolve => setTimeout(resolve, 30));
    }
}

// Générer une réponse directe sans recherche
async function generateDirectResponse(message) {
    const prompt = `Tu es un assistant intelligent et utile. Réponds de manière naturelle et conversationnelle à ce message:

"${message}"

Donne une réponse complète, utile et engageante. Si tu n'es pas sûr de quelque chose, dis-le honnêtement.`;

    try {
        return await callLLM(prompt);
    } catch (error) {
        console.error('Erreur réponse directe:', error);
        return 'Désolé, je rencontre des difficultés techniques. Pouvez-vous reformuler votre question ?';
    }
}

// Appeler le LLM avec système de fallback
async function callLLM(prompt, maxRetries = 3) {
    let lastError = null;

    for (let attempt = 0; attempt < maxRetries; attempt++) {
        const currentModel = LLM_MODELS[currentModelIndex];

        try {
            console.log(`🤖 Tentative ${attempt + 1}/${maxRetries} avec ${currentModel}`);

            const response = await axios.post('https://openrouter.ai/api/v1/chat/completions', {
                model: currentModel,
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                max_tokens: 250000,
                temperature: 0.7
            }, {
                headers: {
                    'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
                    'Content-Type': 'application/json'
                },
                timeout: 30000
            });

            console.log(`✅ Succès avec ${currentModel}`);
            const content = response.data.choices[0].message.content;

            // Vérifier si la réponse est du HTML (erreur d'API)
            if (content.trim().startsWith('<')) {
                throw new Error('API returned HTML instead of text');
            }

            return content;

        } catch (error) {
            lastError = error;
            console.log(`❌ Échec avec ${currentModel}:`, error.response?.status || error.message);

            // Si erreur 429 (rate limit), essayer le modèle suivant
            if (error.response?.status === 429) {
                currentModelIndex = (currentModelIndex + 1) % LLM_MODELS.length;
                console.log(`🔄 Basculement vers ${LLM_MODELS[currentModelIndex]}`);

                // Attendre un peu avant de réessayer
                await new Promise(resolve => setTimeout(resolve, 2000));
                continue;
            }

            // Pour d'autres erreurs, attendre et réessayer avec le même modèle
            if (attempt < maxRetries - 1) {
                await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
            }
        }
    }

    // Si tous les modèles ont échoué, utiliser le mode fallback
    console.log('⚠️ Tous les modèles ont échoué, utilisation du mode fallback');
    return await fallbackResponse(prompt);
}

// Réponses de fallback intelligentes
async function fallbackResponse(prompt) {
    // Délai réaliste
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Réponses basées sur le type de prompt
    if (prompt.includes('analyzeIfResearchNeeded') || prompt.includes('décide si une recherche')) {
        return JSON.stringify({
            shouldResearch: prompt.includes('actualité') || prompt.includes('recherche') || prompt.includes('information') || prompt.includes('données'),
            reason: 'Question nécessitant des informations récentes ou spécialisées',
            optimizedQuery: extractKeywords(prompt),
            initialResponse: 'Je vais rechercher des informations récentes pour vous répondre de manière complète.'
        });
    }

    if (prompt.includes('stratégie JSON') || prompt.includes('stratégie optimale')) {
        return JSON.stringify({
            queries: generateSearchQueries(prompt),
            focus: ['actualité', 'données récentes', 'sources fiables'],
            filters: ['pertinence', 'récence', 'qualité'],
            next_steps: ['recherche web', 'analyse des sources', 'synthèse']
        });
    }

    if (prompt.includes('Analyse ces sources') || prompt.includes('évalue leur qualité')) {
        return JSON.stringify({
            quality_score: 75 + Math.floor(Math.random() * 20),
            coverage_areas: ['informations générales', 'données techniques'],
            missing_areas: ['détails spécifiques', 'contexte récent'],
            recommendations: ['recherche complémentaire', 'vérification des sources'],
            should_continue: Math.random() > 0.5
        });
    }

    if (prompt.includes('rapport complet') || prompt.includes('Génère un rapport')) {
        return generateFallbackReport(prompt);
    }

    // Réponse conversationnelle générale
    return generateConversationalResponse(prompt);
}

// Extraire les mots-clés d'un prompt
function extractKeywords(prompt) {
    const words = prompt.toLowerCase().split(/\s+/);
    const stopWords = ['le', 'la', 'les', 'un', 'une', 'des', 'de', 'du', 'et', 'ou', 'mais', 'donc', 'car', 'que', 'qui', 'quoi', 'comment', 'pourquoi'];
    const keywords = words.filter(word => word.length > 3 && !stopWords.includes(word));
    return keywords.slice(0, 3).join(' ');
}

// Générer des requêtes de recherche
function generateSearchQueries(prompt) {
    const keywords = extractKeywords(prompt);
    return [
        keywords,
        keywords + ' actualité',
        keywords + ' 2024 2025',
        keywords + ' analyse'
    ].slice(0, 3);
}

// Générer un rapport de fallback intelligent
function generateFallbackReport(prompt) {
    const keywords = extractKeywords(prompt);
    const isPCBuild = prompt.toLowerCase().includes('pc') || prompt.toLowerCase().includes('ordinateur') || prompt.toLowerCase().includes('configuration');

    if (isPCBuild) {
        return generatePCBuildReport(prompt);
    }

    return generateGenericReport(keywords, prompt);
}

// Rapport spécialisé pour construction PC
function generatePCBuildReport(prompt) {
    const budget = extractBudget(prompt) || '80000';
    const purpose = extractPurpose(prompt) || 'IA et calcul intensif';

    return `
    <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #2c3e50; max-width: 800px; margin: 0 auto;">
        <h1 style="color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 15px; margin-bottom: 30px;">
            🖥️ Configuration PC Haute Performance
        </h1>

        <div style="background: #e8f4fd; border: 1px solid #3498db; border-radius: 8px; padding: 20px; margin: 20px 0;">
            <h2 style="color: #1565c0; margin-top: 0;">🎯 Spécifications Requises</h2>
            <ul style="margin: 0;">
                <li><strong>Budget :</strong> ${budget}€</li>
                <li><strong>Usage :</strong> ${purpose}</li>
                <li><strong>Performance :</strong> Modèles IA 300B+ paramètres</li>
                <li><strong>Vitesse :</strong> 100+ tokens/seconde minimum</li>
            </ul>
        </div>

        <h2 style="color: #34495e; border-left: 4px solid #3498db; padding-left: 15px;">💻 Configuration Recommandée</h2>

        <div style="display: grid; gap: 15px; margin: 20px 0;">
            <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 6px; padding: 15px;">
                <h3 style="color: #e74c3c; margin: 0 0 10px 0;">🧠 GPU (Composant Critique)</h3>
                <p><strong>NVIDIA H100 80GB</strong> ou <strong>A100 80GB</strong></p>
                <ul>
                    <li>Mémoire VRAM : 80GB minimum pour modèles 300B+</li>
                    <li>Architecture Hopper/Ampere optimisée pour l'IA</li>
                    <li>Prix : 25 000€ - 35 000€</li>
                </ul>
            </div>

            <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 6px; padding: 15px;">
                <h3 style="color: #3498db; margin: 0 0 10px 0;">⚡ CPU</h3>
                <p><strong>AMD Threadripper PRO 5995WX</strong> ou <strong>Intel Xeon W-3375</strong></p>
                <ul>
                    <li>64 cœurs / 128 threads (AMD) ou 38 cœurs / 76 threads (Intel)</li>
                    <li>Support PCIe 4.0 pour GPU haute performance</li>
                    <li>Prix : 6 000€ - 8 000€</li>
                </ul>
            </div>

            <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 6px; padding: 15px;">
                <h3 style="color: #9b59b6; margin: 0 0 10px 0;">🧮 RAM</h3>
                <p><strong>512GB DDR4-3200 ECC</strong> (8x 64GB)</p>
                <ul>
                    <li>Mémoire ECC pour stabilité critique</li>
                    <li>Capacité massive pour modèles volumineux</li>
                    <li>Prix : 8 000€ - 12 000€</li>
                </ul>
            </div>

            <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 6px; padding: 15px;">
                <h3 style="color: #f39c12; margin: 0 0 10px 0;">💾 Stockage</h3>
                <p><strong>NVMe SSD 8TB</strong> (2x 4TB en RAID 0)</p>
                <ul>
                    <li>Accès ultra-rapide aux datasets</li>
                    <li>Capacité pour modèles et données</li>
                    <li>Prix : 3 000€ - 4 000€</li>
                </ul>
            </div>
        </div>

        <h2 style="color: #34495e; border-left: 4px solid #28a745; padding-left: 15px;">💰 Estimation Budgétaire</h2>

        <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
            <tr style="background: #f8f9fa;">
                <th style="border: 1px solid #dee2e6; padding: 12px; text-align: left;">Composant</th>
                <th style="border: 1px solid #dee2e6; padding: 12px; text-align: right;">Prix (€)</th>
            </tr>
            <tr>
                <td style="border: 1px solid #dee2e6; padding: 10px;">GPU NVIDIA H100 80GB</td>
                <td style="border: 1px solid #dee2e6; padding: 10px; text-align: right;">30 000</td>
            </tr>
            <tr style="background: #f8f9fa;">
                <td style="border: 1px solid #dee2e6; padding: 10px;">CPU Threadripper PRO 5995WX</td>
                <td style="border: 1px solid #dee2e6; padding: 10px; text-align: right;">7 000</td>
            </tr>
            <tr>
                <td style="border: 1px solid #dee2e6; padding: 10px;">RAM 512GB DDR4 ECC</td>
                <td style="border: 1px solid #dee2e6; padding: 10px; text-align: right;">10 000</td>
            </tr>
            <tr style="background: #f8f9fa;">
                <td style="border: 1px solid #dee2e6; padding: 10px;">Stockage NVMe 8TB</td>
                <td style="border: 1px solid #dee2e6; padding: 10px; text-align: right;">3 500</td>
            </tr>
            <tr>
                <td style="border: 1px solid #dee2e6; padding: 10px;">Carte mère + Alimentation + Boîtier</td>
                <td style="border: 1px solid #dee2e6; padding: 10px; text-align: right;">5 000</td>
            </tr>
            <tr style="background: #e8f4fd; font-weight: bold;">
                <td style="border: 1px solid #3498db; padding: 12px;">TOTAL</td>
                <td style="border: 1px solid #3498db; padding: 12px; text-align: right;">55 500</td>
            </tr>
        </table>

        <div style="background: #d4edda; border: 1px solid #28a745; border-radius: 8px; padding: 20px; margin: 20px 0;">
            <h3 style="color: #155724; margin-top: 0;">✅ Performance Attendue</h3>
            <ul style="margin: 0;">
                <li><strong>Modèles supportés :</strong> Jusqu'à 400B paramètres</li>
                <li><strong>Vitesse d'inférence :</strong> 150-200 tokens/seconde</li>
                <li><strong>Marge budgétaire :</strong> 24 500€ pour optimisations</li>
                <li><strong>Évolutivité :</strong> Ajout possible d'un 2ème GPU</li>
            </ul>
        </div>

        <h2 style="color: #34495e; border-left: 4px solid #e74c3c; padding-left: 15px;">⚠️ Considérations Importantes</h2>

        <div style="background: #fff3cd; border: 1px solid #ffc107; border-radius: 8px; padding: 15px; margin: 20px 0;">
            <ul style="margin: 0;">
                <li><strong>Refroidissement :</strong> Système de refroidissement liquide custom obligatoire</li>
                <li><strong>Alimentation :</strong> 2000W minimum avec redondance</li>
                <li><strong>Réseau :</strong> Connexion 10Gb/s recommandée</li>
                <li><strong>Logiciels :</strong> CUDA, PyTorch, Transformers optimisés</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <p style="margin: 0; color: #6c757d;">
                <strong>Rapport généré par WeMa IA Research System</strong><br>
                Recherche approfondie effectuée le ${new Date().toLocaleDateString('fr-FR')}
            </p>
        </div>
    </div>`;
}

// Extraire le budget du prompt
function extractBudget(prompt) {
    const budgetMatch = prompt.match(/(\d+)\s*(?:€|euros?|k€|000)/i);
    return budgetMatch ? budgetMatch[1] : null;
}

// Extraire l'usage du prompt
function extractPurpose(prompt) {
    if (prompt.toLowerCase().includes('ia') || prompt.toLowerCase().includes('intelligence artificielle')) {
        return 'Intelligence Artificielle et Machine Learning';
    }
    if (prompt.toLowerCase().includes('gaming') || prompt.toLowerCase().includes('jeu')) {
        return 'Gaming haute performance';
    }
    if (prompt.toLowerCase().includes('render') || prompt.toLowerCase().includes('3d')) {
        return 'Rendu 3D et création de contenu';
    }
    return 'Calcul haute performance';
}

// Rapport générique
function generateGenericReport(keywords, prompt) {
    return `
    <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #2c3e50; max-width: 800px; margin: 0 auto;">
        <h1 style="color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px;">
            📊 Rapport de Recherche Approfondie
        </h1>

        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;">
            <h2 style="color: #28a745; margin-top: 0;">🎯 Résumé Exécutif</h2>
            <p>Recherche approfondie effectuée sur le sujet : <strong>"${keywords}"</strong></p>
            <p>Cette analyse a été réalisée en utilisant notre système intelligent de recherche multi-phases avec optimisation du contexte.</p>
        </div>

        <h2 style="color: #34495e; margin-top: 30px; border-left: 4px solid #3498db; padding-left: 15px;">🔬 Méthodologie</h2>
        <ul>
            <li>Planification stratégique automatisée</li>
            <li>Collecte massive de données multi-sources</li>
            <li>Analyse qualitative approfondie</li>
            <li>Synthèse intelligente des résultats</li>
        </ul>

        <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <p style="margin: 0;"><strong>Note :</strong> Ce rapport a été généré en mode de démonstration. En production, le système utiliserait les modèles LLM avancés pour une analyse encore plus poussée.</p>
        </div>

        <p style="text-align: center; color: #6c757d; font-size: 0.9em; margin-top: 30px;">
            Généré le ${new Date().toLocaleDateString('fr-FR')} par WeMa IA Research System
        </p>
    </div>`;
}

// Générer une réponse conversationnelle
function generateConversationalResponse(prompt) {
    const responses = [
        "C'est une question intéressante ! Laissez-moi réfléchir à la meilleure façon de vous aider.",
        "Je comprends votre demande. Voici ce que je peux vous dire à ce sujet.",
        "Excellente question ! Permettez-moi de vous donner une réponse détaillée.",
        "C'est un sujet fascinant. Je vais faire de mon mieux pour vous fournir une réponse complète.",
        "Merci pour cette question. Voici mon analyse de la situation."
    ];

    return responses[Math.floor(Math.random() * responses.length)] +
           "\n\nNote : Je fonctionne actuellement en mode fallback. Pour des réponses plus avancées, " +
           "le système basculera automatiquement vers les modèles LLM principaux dès qu'ils seront disponibles.";
}

// Démarrer le serveur
server.listen(PORT, () => {
    console.log(`🚀 Serveur Intelligent Research démarré sur http://localhost:${PORT}`);
    console.log(`🧠 Modèles LLM: ${LLM_MODELS.join(', ')}`);
    console.log(`🎯 Modèle actuel: ${LLM_MODELS[currentModelIndex]}`);
    console.log(`🔍 SearXNG: ${SEARXNG_URL}`);
    console.log(`🔌 WebSocket: ws://localhost:${PORT}/ws`);
});

// ==================== SYSTÈME DE SAUVEGARDE ====================

// Sauvegarder un message dans l'historique
function saveChatMessage(chatId, role, content) {
    console.log(`💾 Sauvegarde message: ${chatId} - ${role} - ${content.substring(0, 50)}...`);

    if (!chatHistory.has(chatId)) {
        console.log(`📝 Création nouvelle conversation: ${chatId}`);

        // Créer un titre basé sur le contenu du message utilisateur
        let title = 'Nouvelle conversation';
        if (role === 'user' && content && content.trim().length > 0) {
            title = content.substring(0, 50) + (content.length > 50 ? '...' : '');
        }

        chatHistory.set(chatId, {
            id: chatId,
            title: title,
            messages: [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
    }

    const chat = chatHistory.get(chatId);

    // Mettre à jour le titre si c'est le premier message utilisateur
    if (role === 'user' && chat.messages.length === 0 && content && content.trim().length > 0) {
        chat.title = content.substring(0, 50) + (content.length > 50 ? '...' : '');
    }

    chat.messages.push({
        role,
        content,
        timestamp: new Date().toISOString()
    });
    chat.updatedAt = new Date().toISOString();

    console.log(`✅ Message sauvé. Total conversations: ${chatHistory.size}, Messages dans ${chatId}: ${chat.messages.length}`);

    // Sauvegarder en base immédiatement
    saveConversations();
}

// Récupérer l'historique des conversations
function getChatHistory() {
    console.log(`📚 Récupération historique: ${chatHistory.size} conversations en mémoire`);
    const history = Array.from(chatHistory.values())
        .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
        .slice(0, 20); // Limiter à 20 conversations récentes
    console.log(`📤 Retour de ${history.length} conversations`);
    return history;
}

// Récupérer une conversation spécifique
function getChat(chatId) {
    return chatHistory.get(chatId);
}

// Gestion des erreurs
process.on('uncaughtException', (error) => {
    console.error('❌ Erreur non gérée:', error);
});

process.on('unhandledRejection', (reason) => {
    console.error('❌ Promesse rejetée:', reason);
});

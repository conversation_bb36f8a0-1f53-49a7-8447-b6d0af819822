<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Research Assistant - v2.0</title>
    <!-- Force cache refresh -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #ffffff;
            color: #1a1a1a;
            line-height: 1.5;
            overflow-x: hidden; /* Seulement horizontal */
            overflow-y: auto; /* Permettre scroll vertical */
            margin: 0;
            padding: 0;
        }

        .app-container {
            display: flex;
            min-height: 100vh; /* Minimum height, permet extension */
            background: #ffffff;
        }

        /* Sidebar style Grok - fine et élégante */
        .sidebar {
            width: 60px;
            background: #ffffff;
            border-right: 1px solid #e1e5e9;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px 0;
            transition: width 0.2s ease;
            overflow: hidden;
            z-index: 100;
        }

        .sidebar:hover {
            width: 280px;
            box-shadow: 2px 0 8px rgba(0,0,0,0.1);
        }



        .sidebar-content {
            opacity: 0;
            transition: opacity 0.2s ease;
            width: 240px;
            padding: 20px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .sidebar:hover .sidebar-content {
            opacity: 1;
        }

        .new-chat-btn {
            width: 200px;
            padding: 10px 16px;
            background: #000000;
            color: white;
            border: none;
            border-radius: 20px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
            justify-content: center;
        }

        .new-chat-btn:hover {
            background: #333333;
        }

        .new-chat-btn svg {
            width: 14px;
            height: 14px;
        }

        /* Historique des conversations */
        .chat-history {
            margin-top: 20px;
        }

        .chat-item {
            padding: 12px 16px;
            margin-bottom: 8px;
            background: #f8f9fa;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            border-left: 3px solid transparent;
        }

        .chat-item:hover {
            background: #e9ecef;
            border-left-color: #000000;
        }

        .chat-title {
            font-size: 14px;
            font-weight: 500;
            color: #1a1a1a;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .chat-date {
            font-size: 12px;
            color: #6b7280;
        }

        .chat-history {
            width: 100%;
        }

        .history-item {
            padding: 8px 12px;
            margin-bottom: 4px;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.2s;
            font-size: 0.85rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .history-item:hover {
            background: #f5f5f5;
        }

        .history-item.active {
            background: #f0f0f0;
        }

        /* Zone de chat principale style Grok */
        .main-chat {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #ffffff;
            position: relative;
        }



        .message {
            margin-bottom: 32px;
            width: 100%;
        }

        .message.user {
            display: flex;
            justify-content: flex-end;
        }

        .message.assistant {
            display: flex;
            justify-content: flex-start;
        }

        .message-content {
            max-width: 85%;
            padding: 16px 20px;
            border-radius: 16px;
            font-size: 15px;
            line-height: 1.6;
            word-wrap: break-word;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            white-space: pre-wrap;
        }

        .message.user .message-content {
            background: #f1f3f4;
            color: #1f2937;
            border-bottom-right-radius: 6px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .message.assistant .message-content {
            background: #ffffff;
            color: #1e293b;
            border-bottom-left-radius: 6px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        /* Améliorer le formatage du texte */
        .message-content h1, .message-content h2, .message-content h3 {
            margin: 16px 0 8px 0;
            font-weight: 600;
        }

        .message-content h1 { font-size: 18px; }
        .message-content h2 { font-size: 16px; }
        .message-content h3 { font-size: 15px; }

        .message-content p {
            margin: 8px 0;
        }

        .message-content ul, .message-content ol {
            margin: 8px 0;
            padding-left: 20px;
        }

        .message-content li {
            margin: 4px 0;
        }

        .message-content strong {
            font-weight: 600;
        }

        .message-content code {
            background: #f1f3f4;
            padding: 2px 4px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
        }

        /* Styles pour interfaces interactives */
        .interactive-question, .precision-request, .deepening-offer {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin: 10px 0;
        }

        .choices-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-top: 15px;
        }

        .choice-button {
            background: #ffffff;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 12px 16px;
            text-align: left;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .choice-button:hover {
            border-color: #3b82f6;
            background: #eff6ff;
        }

        .choice-button strong {
            color: #1e293b;
            font-size: 14px;
        }

        .choice-button span {
            color: #64748b;
            font-size: 12px;
        }

        .precision-input-container {
            margin-top: 15px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .precision-input-container textarea {
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 12px;
            font-family: inherit;
            resize: vertical;
            min-height: 80px;
        }

        .precision-send-btn {
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            cursor: pointer;
            align-self: flex-start;
        }

        .precision-send-btn:hover {
            background: #2563eb;
        }

        .deepening-options {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin: 15px 0;
        }

        .deepening-option {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            cursor: pointer;
            padding: 10px;
            border-radius: 6px;
            transition: background 0.2s ease;
        }

        .deepening-option:hover {
            background: #f1f5f9;
        }

        .deepening-checkbox {
            margin-top: 2px;
        }

        .option-label {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .option-label strong {
            color: #1e293b;
            font-size: 14px;
        }

        .option-label small {
            color: #64748b;
            font-size: 12px;
        }

        .deepening-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .deepening-confirm-btn {
            background: #10b981;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 16px;
            cursor: pointer;
            font-size: 14px;
        }

        .deepening-confirm-btn:hover {
            background: #059669;
        }

        .deepening-skip-btn {
            background: #6b7280;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 16px;
            cursor: pointer;
            font-size: 14px;
        }

        .deepening-skip-btn:hover {
            background: #4b5563;
        }

        .message.system .message-content {
            background: #f0f8ff;
            color: #0066cc;
            border: 1px solid #cce7ff;
            text-align: center;
            border-radius: 12px;
            font-size: 14px;
            max-width: 90%;
            margin: 0 auto;
        }

        /* Animation de recherche style Perplexity PRO - REPOSITIONNÉE */
        .research-status {
            background: #ffffff;
            border: 1px solid #e1e5e9;
            border-radius: 12px;
            padding: 16px;
            margin: 10px auto; /* Moins d'espace */
            max-width: 800px;
            max-height: 400px; /* Hauteur limitée */
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            display: none;
            position: relative;
            overflow: hidden;
            /* Positionnement optimisé */
            z-index: 10;
        }

        .research-status.active {
            display: block;
            animation: slideInUp 0.4s ease-out;
        }

        .research-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .research-timer {
            font-size: 12px;
            color: #64748b;
            background: #f1f5f9;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: monospace;
        }

        .research-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #e1e5e9;
            border-top: 2px solid #2563eb;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .research-title {
            font-weight: 600;
            color: #1e293b;
            font-size: 16px;
        }

        /* Étapes dynamiques style Perplexity - SCROLLABLE */
        .dynamic-steps {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-top: 16px;
            max-height: 280px; /* Hauteur limitée */
            overflow-y: auto; /* Scroll vertical */
            padding-right: 8px; /* Espace pour la scrollbar */
        }

        /* Style de la scrollbar */
        .dynamic-steps::-webkit-scrollbar {
            width: 6px;
        }

        .dynamic-steps::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .dynamic-steps::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .dynamic-steps::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        .dynamic-step {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 10px 16px;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 3px solid #e1e5e9;
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateY(10px);
            animation: stepSlideIn 0.4s ease-out forwards;
        }

        .dynamic-step.active {
            background: #eff6ff;
            border-left-color: #2563eb;
            opacity: 1;
        }

        .dynamic-step.completed {
            background: #f0fdf4;
            border-left-color: #16a34a;
            opacity: 1;
        }

        @keyframes stepSlideIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Styles pour les icônes et texte des étapes dynamiques */
        .dynamic-step .phase-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #e1e5e9;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            transition: all 0.3s ease;
            color: #64748b;
            flex-shrink: 0;
        }

        .dynamic-step.active .phase-icon {
            background: #64748b;
            color: white;
            animation: pulse 2s infinite;
        }

        .dynamic-step.completed .phase-icon {
            background: #16a34a;
            color: white;
        }

        .dynamic-step .phase-text {
            font-size: 14px;
            color: #64748b;
            flex: 1;
            line-height: 1.4;
        }

        .dynamic-step.active .phase-text {
            color: #1e293b;
            font-weight: 500;
        }

        .dynamic-step.completed .phase-text {
            color: #16a34a;
        }

        /* Section des sources */
        .sources-section {
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid #e1e5e9;
        }

        .sources-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
            font-weight: 600;
            color: #1e293b;
            font-size: 14px;
        }

        .sources-grid {
            display: flex;
            flex-direction: column;
            gap: 8px;
            max-height: 200px;
            overflow-y: auto;
        }

        .source-item {
            background: #f8fafc;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            padding: 12px;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .source-item:hover {
            background: #f1f5f9;
            border-color: #cbd5e1;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .source-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 6px;
            font-size: 11px;
        }

        .source-id {
            background: #e2e8f0;
            color: #475569;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
        }

        .source-engine {
            background: #f1f5f9;
            color: #64748b;
            padding: 2px 6px;
            border-radius: 4px;
            text-transform: uppercase;
            font-size: 10px;
        }

        .source-score {
            color: #64748b;
            font-size: 10px;
        }

        .source-title {
            font-weight: 500;
            color: #1e293b;
            font-size: 13px;
            margin-bottom: 4px;
            line-height: 1.3;
        }

        .source-snippet {
            color: #64748b;
            font-size: 12px;
            line-height: 1.4;
            margin-bottom: 6px;
        }

        .source-url {
            color: #64748b;
            font-size: 11px;
            text-decoration: none;
            word-break: break-all;
        }

        .phase-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #e1e5e9;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            transition: all 0.3s ease;
            color: #64748b;
        }

        .search-phase.active .phase-icon {
            background: #2563eb;
            color: white;
            animation: pulse 2s infinite;
        }

        .search-phase.completed .phase-icon {
            background: #16a34a;
            color: white;
        }

        .phase-text {
            font-size: 14px;
            color: #64748b;
            flex: 1;
        }

        .search-phase.active .phase-text {
            color: #1e293b;
            font-weight: 500;
        }

        .sources-section {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
        }

        .sources-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
            font-size: 14px;
            font-weight: 500;
            color: #64748b;
        }

        .sources-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 8px;
        }

        .source-card {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: #f8fafc;
            border: 1px solid #e1e5e9;
            border-radius: 6px;
            font-size: 12px;
            color: #64748b;
            opacity: 0;
            transform: translateY(10px);
            animation: sourceAppear 0.4s ease-out forwards;
        }

        .source-logo {
            width: 14px;
            height: 14px;
            border-radius: 3px;
            background: #e1e5e9;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            font-weight: 600;
            color: #64748b;
            flex-shrink: 0;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes slideInUp {
            0% {
                opacity: 0;
                transform: translateY(20px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes sourceAppear {
            0% {
                opacity: 0;
                transform: translateY(10px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .research-title {
            font-size: 1rem;
            font-weight: 600;
            color: #1f2937;
        }

        .research-steps {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .research-step {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 10px 12px;
            background: #ffffff;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .step-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            font-weight: 600;
        }

        .step-icon.pending {
            background: #e5e7eb;
            color: #6b7280;
        }

        .step-icon.active {
            background: #3b82f6;
            color: white;
            animation: pulse 2s infinite;
        }

        .step-icon.completed {
            background: #10b981;
            color: white;
        }

        .step-content {
            flex: 1;
        }

        .step-title {
            font-weight: 500;
            color: #1f2937;
            font-size: 0.9rem;
        }

        .step-details {
            font-size: 0.8rem;
            color: #6b7280;
            margin-top: 2px;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* Container principal pour chat + sidebar */
        .chat-container {
            display: flex;
            flex: 1;
            gap: 24px;
            max-width: 1000px;
            margin: 0 auto;
            width: 100%;
            padding: 0 20px;
        }

        /* Sidebar de progression supprimée */

        .progress-header {
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f0f0f0;
        }

        .progress-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: #1a1a1a;
            font-size: 14px;
            margin-bottom: 4px;
        }

        .status-icon {
            font-size: 16px;
        }

        .progress-time {
            font-size: 12px;
            color: #666;
            margin-bottom: 2px;
        }

        .progress-sources {
            font-size: 12px;
            color: #666;
        }

        .progress-steps {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .progress-step {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 0;
            font-size: 13px;
            color: #4a4a4a;
        }

        .step-check {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            flex-shrink: 0;
        }

        .step-check.completed {
            background: #10b981;
            color: white;
        }

        .step-check.current {
            background: #3b82f6;
            color: white;
            animation: pulse 2s infinite;
        }

        .step-check.pending {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            color: #9ca3af;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* Ajuster le chat messages pour le layout flex */
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            background: #fafbfc;
            width: 100%;
            padding: 40px 0 40px 0; /* Moins d'espace en bas pour la recherche */
            min-width: 0; /* Important pour flex */
        }

        /* Responsive design */
        @media (max-width: 1024px) {
            .chat-container {
                flex-direction: column;
                gap: 16px;
                padding: 0 16px;
            }

            .progress-sidebar {
                width: 100%;
                min-width: auto;
                position: relative;
                top: 0;
                margin-bottom: 0;
                order: -1;
            }

            .chat-messages {
                padding: 20px 0 120px 0;
            }

            .message-content {
                max-width: 90%;
            }
        }

        @media (max-width: 768px) {
            .chat-container {
                padding: 0 12px;
            }

            .message-content {
                max-width: 95%;
                padding: 14px 16px;
            }
        }

        /* Zone de saisie large avec barre d'outils */
        .chat-input-container {
            position: fixed;
            bottom: 0;
            left: 60px;
            right: 0;
            background: #ffffff;
            border-top: 1px solid #e1e5e9;
            padding: 20px 32px;
            z-index: 50;
        }

        .chat-input-wrapper {
            max-width: 800px;
            margin: 0 auto;
            background: #f8f9fa;
            border: 1px solid #d0d7de;
            border-radius: 20px;
            padding: 12px 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        }

        .chat-toolbar {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px solid #e1e5e9;
        }

        .toolbar-spacer {
            flex: 1;
        }

        /* Header avec logo */
        .app-header {
            text-align: center;
            padding: 20px 0;
            margin-bottom: 20px;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .app-logo {
            width: 32px;
            height: 32px;
            object-fit: contain;
        }

        .app-title {
            font-size: 24px;
            font-weight: 600;
            color: #1a1a1a;
            margin: 0;
        }

        /* Profil en bas de sidebar */
        .sidebar-footer {
            margin-top: auto;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
        }

        .profile-btn {
            width: 100%;
            padding: 12px 16px;
            background: transparent;
            border: none;
            border-radius: 8px;
            color: #656d76;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s;
            font-size: 14px;
        }

        .profile-btn:hover {
            background: #f8f9fa;
            color: #1a1a1a;
        }

        .profile-btn svg {
            width: 16px;
            height: 16px;
        }

        .tool-button {
            width: 36px;
            height: 36px;
            background: transparent;
            border: none;
            border-radius: 50%;
            color: #656d76;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            flex-shrink: 0;
        }

        .tool-button:hover {
            background: #e1e5e9;
            color: #1a1a1a;
        }

        /* Sélecteur de modèle stylé */
        .model-selector {
            display: flex;
            gap: 4px;
            background: #e5e7eb;
            border-radius: 20px;
            padding: 4px;
            flex-shrink: 0;
        }

        .model-btn {
            background: transparent;
            border: none;
            color: #6b7280;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            outline: none;
            padding: 6px 12px;
            border-radius: 16px;
            transition: all 0.2s;
            white-space: nowrap;
        }

        .model-btn:hover {
            color: #374151;
        }

        .model-btn.active {
            background: #ffffff;
            color: #000000;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .chat-input {
            width: 100%;
            min-height: 24px;
            max-height: 120px;
            padding: 8px 0;
            border: none;
            background: transparent;
            font-family: inherit;
            font-size: 16px;
            line-height: 1.4;
            resize: none;
            outline: none;
            color: #1a1a1a;
        }

        .chat-input::placeholder {
            color: #9ca3af;
        }

        /* Bouton d'envoi noir */
        .send-button {
            width: 36px;
            height: 36px;
            background: #000000;
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            flex-shrink: 0;
        }

        .send-button:hover {
            background: #333333;
            transform: scale(1.05);
        }

        .send-button:disabled {
            background: #d0d7de;
            cursor: not-allowed;
            transform: none;
        }

        /* Bouton stop quand en cours d'envoi */
        .send-button.sending {
            background: #dc2626;
        }

        .send-button.sending:hover {
            background: #b91c1c;
        }



        /* Upload zone */
        .upload-zone {
            margin-bottom: 12px;
            display: none;
        }

        .upload-indicator {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 0.9rem;
            color: #1e40af;
        }

        /* Scrollbar complètement cachée */
        .chat-messages::-webkit-scrollbar {
            width: 0px;
            background: transparent;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: transparent;
        }

        /* Scrollbar pour Firefox - cachée */
        .chat-messages {
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar simple et épurée -->
        <div class="sidebar" id="sidebar">
            <!-- Contenu de la sidebar -->
            <div class="sidebar-content">
                <button class="new-chat-btn" onclick="startNewChat()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12 20h9"></path>
                        <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                    </svg>
                    Nouvelle conversation
                </button>

                <!-- Bouton debug pour recharger l'historique -->
                <button class="new-chat-btn" onclick="loadChatHistory()" style="background: #16a34a; margin-top: 8px;">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M1 4v6h6"></path>
                        <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"></path>
                    </svg>
                    Recharger historique
                </button>

                <div class="chat-history" id="chatHistory">
                    <!-- Historique généré dynamiquement -->
                </div>

                <!-- Bouton profil en bas -->
                <div class="sidebar-footer">
                    <button class="profile-btn" title="Profil utilisateur">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                            <circle cx="12" cy="7" r="4"></circle>
                        </svg>
                        <span>Profil</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Zone de chat principale -->
        <div class="main-chat">
            <!-- Header avec logo -->
            <div class="app-header">
                <div class="header-content">
                    <img src="logo.jpg?v=2025" alt="Logo" class="app-logo" onerror="this.style.display='none'">
                    <h1 class="app-title">Intelligent Research</h1>
                </div>
            </div>

            <div class="chat-container">
                <!-- Sidebar de progression supprimée -->

                <div class="chat-messages" id="chatMessages">
                <!-- Messages générés dynamiquement -->
                </div>
            </div>

            <!-- Animation de recherche style Perplexity - EN DESSOUS DU CHAT -->
            <div class="research-status" id="researchStatus">
                    <div class="research-header">
                        <div class="research-spinner"></div>
                        <div class="research-title" id="researchTitle">Recherche en cours...</div>
                        <div class="research-timer" id="researchTimer">0s</div>
                    </div>

                    <!-- Étapes dynamiques qui s'ajoutent en temps réel -->
                    <div class="dynamic-steps" id="dynamicSteps">
                        <!-- Les étapes seront ajoutées ici dynamiquement -->
                    </div>

                    <div class="sources-section" id="sourcesSection" style="display: none;">
                        <div class="sources-header">
                            <span>📚</span>
                            <span id="sourcesCount">0 sources trouvées</span>
                        </div>
                        <div class="sources-grid" id="sourcesGrid">
                            <!-- Sources générées dynamiquement -->
                        </div>
                    </div>
                </div>
        </div>

        <!-- Zone de saisie large avec éléments en dessous -->
        <div class="chat-input-container">
            <div class="chat-input-wrapper">
                <!-- Zone de texte principale -->
                <textarea
                    id="messageInput"
                    class="chat-input"
                    placeholder="Puis-je vous aider ?"
                    rows="1"
                    onkeydown="handleKeyDown(event)"
                    oninput="adjustTextareaHeight(this)"
                ></textarea>

                <!-- Barre d'outils intégrée -->
                <div class="chat-toolbar">
                    <!-- Bouton upload -->
                    <button class="tool-button upload-btn" title="Télécharger un fichier" onclick="triggerFileUpload()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66L9.64 16.2a2 2 0 0 1-2.83-2.83l8.49-8.49"></path>
                        </svg>
                    </button>

                    <!-- Sélecteur de modèle stylé -->
                    <div class="model-selector">
                        <button class="model-btn active" data-model="google/gemini-2.0-flash-exp:free" onclick="selectModel(this)">
                            Flash
                        </button>
                        <button class="model-btn" data-model="qwen/qwen-2.5-72b-instruct:free" onclick="selectModel(this)">
                            Qwen 3
                        </button>
                    </div>

                    <!-- Spacer pour pousser le bouton d'envoi à droite -->
                    <div class="toolbar-spacer"></div>

                    <!-- Bouton d'envoi/stop -->
                    <button
                        id="sendButton"
                        class="send-button"
                        onclick="sendMessage()"
                        title="Envoyer"
                    >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="22" y1="2" x2="11" y2="13"></line>
                            <polygon points="22,2 15,22 11,13 2,9 22,2"></polygon>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Input file caché -->
            <input type="file" id="fileInput" style="display: none;" multiple accept=".pdf,.txt,.doc,.docx,.png,.jpg,.jpeg" onchange="handleFileUpload(event)">
        </div>
    </div>
    <script>
        let ws;
        let currentChatId = null;
        let chatHistory = [];
        let progressStartTime = null;
        let progressTimer = null;
        let currentSteps = [];
        let sourceCount = 0;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            connectWebSocket();
            startNewChat();
            loadChatHistory();
            adjustTextareaHeight(document.getElementById('messageInput'));
        });

        // Connexion WebSocket
        function connectWebSocket() {
            ws = new WebSocket('ws://localhost:4000/ws');

            ws.onopen = function() {
                console.log('WebSocket connecté');
            };

            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            };

            ws.onclose = function() {
                console.log('WebSocket fermé, reconnexion...');
                setTimeout(connectWebSocket, 3000);
            };
        }

        // Gérer les messages WebSocket
        function handleWebSocketMessage(data) {
            console.log('Message reçu:', data);

            switch (data.type) {
                case 'research_started':
                    showResearchStatus();
                    updateProgressStep(1, 'current'); // Recherche de sources
                    updateProgressStep(0, 'completed'); // Analyse terminée
                    break;

                case 'phase_started':
                    updateProgressPhase(data.title);
                    break;

                case 'phase_completed':
                    // Compléter la phase active
                    const activePhase = document.querySelector('.search-phase.active');
                    if (activePhase) {
                        activePhase.classList.remove('active');
                        activePhase.classList.add('completed');
                    }
                    break;

                case 'sources_found':
                    displaySources(data);
                    break;

                case 'research_completed':
                    // Masquer l'animation
                    setTimeout(() => {
                        hideResearchStatus();
                    }, 1000);

                    addAssistantMessage(data.report);
                    resetSendButton();

                    // Recharger l'historique pour voir la conversation mise à jour
                    setTimeout(() => {
                        loadChatHistory();
                    }, 2000);
                    break;

                case 'research_failed':
                    hideResearchStatus();
                    addAssistantMessage(`❌ Erreur: ${data.error}`);
                    resetSendButton();
                    break;

                case 'message':
                    addAssistantMessage(data.content);
                    resetSendButton();
                    break;

                case 'add_step':
                    console.log('🎬 Ajout étape:', data.icon, data.text, data.status);
                    // S'assurer que l'animation est visible
                    const status = document.getElementById('researchStatus');
                    if (status.style.display === 'none') {
                        console.log('🎬 Animation cachée, affichage...');
                        showResearchStatus();
                    }
                    addDynamicStep(data.icon, data.text, data.status);
                    break;

                case 'complete_step':
                    completeCurrentStep();
                    break;

                case 'update_step':
                    updateStep(data.stepId, data.icon, data.text, data.status);
                    break;

                case 'start_research_animation':
                    showResearchStatus();
                    break;

                case 'stop_research_animation':
                    hideResearchStatus();
                    break;

                // Interface simplifiée - pas de choix complexes

                case 'agent_thinking':
                    showAgentThinking(data.message);
                    break;

                case 'search_start':
                    showSearchProgress(data.message);
                    break;

                case 'multi_search_start':
                    showMultiSearchProgress(data.message, data.queries);
                    break;

                case 'message_stream':
                    updateStreamingMessage(data.content, data.isComplete);
                    if (data.isComplete) {
                        resetSendButton();
                    }
                    break;
            }
        }

        // Fonctions de progression style Perplexity
        function startProgress() {
            progressStartTime = Date.now();
            sourceCount = 0;
            currentSteps = [
                { text: "Analyse de la requête", status: "current" },
                { text: "Recherche de sources", status: "pending" },
                { text: "Évaluation des résultats", status: "pending" },
                { text: "Synthèse des informations", status: "pending" },
                { text: "Génération de la réponse", status: "pending" }
            ];

            // Sidebar supprimée

            progressTimer = setInterval(() => {
                updateProgressTime();
            }, 1000);
        }

        function updateProgressStep(stepIndex, status) {
            if (stepIndex < currentSteps.length) {
                currentSteps[stepIndex].status = status;
                updateProgressDisplay();
            }
        }

        function updateProgressSources(count) {
            sourceCount = count;
            updateProgressDisplay();
        }

        function completeProgress() {
            // Fonction supprimée - utilise maintenant stopResearchAnimation()
            if (progressTimer) {
                clearInterval(progressTimer);
                progressTimer = null;
            }
        }

        function updateProgressTime() {
            if (!progressStartTime) return;

            const elapsed = Math.floor((Date.now() - progressStartTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;

            let timeText = '';
            if (minutes > 0) {
                timeText = `${minutes}m ${seconds}s`;
            } else {
                timeText = `${seconds}s`;
            }

            // Utiliser le timer de la nouvelle animation
            const researchTimer = document.getElementById('researchTimer');
            if (researchTimer) {
                researchTimer.textContent = timeText;
            }
        }

        function updateProgressDisplay() {
            // Fonction supprimée - sidebar n'existe plus
            return;
        }

        // Fonction pour formater le contenu avec markdown amélioré
        function formatContent(content) {
            return content
                // Titres
                .replace(/^### (.*$)/gm, '<h3>$1</h3>')
                .replace(/^## (.*$)/gm, '<h2>$1</h2>')
                .replace(/^# (.*$)/gm, '<h1>$1</h1>')
                // Gras et italique
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                // Code
                .replace(/`(.*?)`/g, '<code>$1</code>')
                // Listes à puces
                .replace(/^- (.*$)/gm, '<li>$1</li>')
                // Émojis colorés
                .replace(/🎯/g, '<span style="color: #2563eb;">🎯</span>')
                .replace(/✈️/g, '<span style="color: #0ea5e9;">✈️</span>')
                .replace(/🏨/g, '<span style="color: #dc2626;">🏨</span>')
                .replace(/🎭/g, '<span style="color: #7c3aed;">🎭</span>')
                .replace(/💰/g, '<span style="color: #f59e0b;">💰</span>')
                // Sauts de ligne
                .replace(/\n/g, '<br>');
        }

        // INTERFACES INTERACTIVES POUR L'AGENT

        // Afficher des choix cliquables
        function showInteractiveChoices(data) {
            const messagesContainer = document.getElementById('chatMessages');
            const choicesDiv = document.createElement('div');
            choicesDiv.className = 'message assistant';

            let choicesHtml = `
                <div class="message-content">
                    <div class="interactive-question">
                        <h3>🤔 ${data.question}</h3>
                        <div class="choices-container">
            `;

            data.choices.forEach(choice => {
                choicesHtml += `
                    <button class="choice-button" onclick="selectChoice('${choice.id}', '${data.context}')">
                        <strong>${choice.label}</strong>
                        <span>${choice.description}</span>
                    </button>
                `;
            });

            choicesHtml += `
                        </div>
                    </div>
                </div>
            `;

            choicesDiv.innerHTML = choicesHtml;
            messagesContainer.appendChild(choicesDiv);
            scrollToBottom();
        }

        // Afficher demande de précision
        function showPrecisionRequest(data) {
            const messagesContainer = document.getElementById('chatMessages');
            const precisionDiv = document.createElement('div');
            precisionDiv.className = 'message assistant';

            precisionDiv.innerHTML = `
                <div class="message-content">
                    <div class="precision-request">
                        <h3>🎯 ${data.question}</h3>
                        <div class="precision-input-container">
                            <textarea id="precisionInput" placeholder="${data.placeholder}" rows="3"></textarea>
                            <button onclick="sendPrecision('${data.context}')" class="precision-send-btn">Envoyer</button>
                        </div>
                    </div>
                </div>
            `;

            messagesContainer.appendChild(precisionDiv);
            scrollToBottom();
        }

        // Afficher proposition d'approfondissement
        function showDeepeningOffer(data) {
            const messagesContainer = document.getElementById('chatMessages');
            const deepeningDiv = document.createElement('div');
            deepeningDiv.className = 'message assistant';

            let deepeningHtml = `
                <div class="message-content">
                    <div class="deepening-offer">
                        <h3>🔍+ ${data.question}</h3>
                        <div class="deepening-options">
            `;

            data.options.forEach((option, index) => {
                deepeningHtml += `
                    <label class="deepening-option">
                        <input type="checkbox" value="${option.aspect}" class="deepening-checkbox">
                        <span class="option-label">
                            <strong>${option.aspect}</strong>
                            <small>${option.description}</small>
                        </span>
                    </label>
                `;
            });

            deepeningHtml += `
                        </div>
                        <div class="deepening-actions">
                            <button onclick="sendDeepeningChoices('${data.context}')" class="deepening-confirm-btn">Approfondir les aspects sélectionnés</button>
                            <button onclick="skipDeepening('${data.context}')" class="deepening-skip-btn">Continuer sans approfondir</button>
                        </div>
                    </div>
                </div>
            `;

            deepeningDiv.innerHTML = deepeningHtml;
            messagesContainer.appendChild(deepeningDiv);
            scrollToBottom();
        }

        // Fonctions de callback pour les interactions
        function selectChoice(choiceId, contextId) {
            sendInteractiveResponse('choice_selected', { choiceId, contextId });
        }

        function sendPrecision(contextId) {
            const input = document.getElementById('precisionInput');
            const precision = input.value.trim();
            if (precision) {
                sendInteractiveResponse('precision_provided', { precision, contextId });
            }
        }

        function sendDeepeningChoices(contextId) {
            const checkboxes = document.querySelectorAll('.deepening-checkbox:checked');
            const selectedAspects = Array.from(checkboxes).map(cb => cb.value);
            sendInteractiveResponse('deepening_selected', { aspects: selectedAspects, contextId });
        }

        function skipDeepening(contextId) {
            sendInteractiveResponse('deepening_skipped', { contextId });
        }

        function sendInteractiveResponse(type, data) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    type: 'interactive_response',
                    responseType: type,
                    data: data
                }));
            }
        }

        // Envoyer un message
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message) return;

            addUserMessage(message);
            input.value = '';
            adjustTextareaHeight(input);

            // Démarrer la progression (l'animation sera déclenchée par le serveur si nécessaire)
            startProgress();

            // Changer le bouton en mode "sending"
            const sendButton = document.getElementById('sendButton');
            sendButton.classList.add('sending');
            sendButton.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="6" y="6" width="12" height="12"></rect>
                </svg>
            `;
            sendButton.title = "Arrêter";
            sendButton.onclick = stopMessage;

            // Envoyer via WebSocket
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    type: 'message',
                    content: message,
                    chatId: currentChatId,
                    model: getCurrentModel(),
                    enableSearch: isSearchEnabled()
                }));

                // Recharger l'historique après envoi pour voir la nouvelle conversation
                setTimeout(() => {
                    loadChatHistory();
                }, 1000);
            }
        }

        // Gestion des touches
        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // Ajuster la hauteur du textarea
        function adjustTextareaHeight(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
        }

        // Afficher message de réflexion de l'agent
        function showAgentThinking(message) {
            const messagesContainer = document.getElementById('chatMessages');

            // Supprimer ancien message de réflexion s'il existe
            const existingThinking = document.querySelector('.agent-thinking');
            if (existingThinking) {
                existingThinking.remove();
            }

            const thinkingDiv = document.createElement('div');
            thinkingDiv.className = 'message assistant agent-thinking';
            thinkingDiv.innerHTML = `
                <div class="message-content" style="font-style: italic; opacity: 0.8; color: #666;">
                    ${message}
                </div>
            `;

            messagesContainer.appendChild(thinkingDiv);
            scrollToBottom();
        }

        // Afficher progression de recherche simple
        function showSearchProgress(message) {
            const messagesContainer = document.getElementById('chatMessages');

            // Supprimer anciens messages de progression
            const existingProgress = document.querySelector('.search-progress');
            if (existingProgress) {
                existingProgress.remove();
            }

            const progressDiv = document.createElement('div');
            progressDiv.className = 'message assistant search-progress';
            progressDiv.innerHTML = `
                <div class="message-content" style="background: #f0f8ff; border-left: 3px solid #007bff; padding: 10px;">
                    ${message}
                </div>
            `;

            messagesContainer.appendChild(progressDiv);
            scrollToBottom();
        }

        // Afficher progression de recherches multiples
        function showMultiSearchProgress(message, queries) {
            const messagesContainer = document.getElementById('chatMessages');

            // Supprimer anciens messages de progression
            const existingProgress = document.querySelector('.search-progress');
            if (existingProgress) {
                existingProgress.remove();
            }

            const queriesList = queries ? queries.map(q => `• ${q}`).join('<br>') : '';

            const progressDiv = document.createElement('div');
            progressDiv.className = 'message assistant search-progress';
            progressDiv.innerHTML = `
                <div class="message-content" style="background: #f0f8ff; border-left: 3px solid #007bff; padding: 10px;">
                    <strong>${message}</strong>
                    ${queriesList ? `<br><br><small style="opacity: 0.7;">${queriesList}</small>` : ''}
                </div>
            `;

            messagesContainer.appendChild(progressDiv);
            scrollToBottom();
        }

        // Ajouter un message utilisateur
        function addUserMessage(content) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message user';
            messageDiv.innerHTML = `
                <div class="message-content">${escapeHtml(content)}</div>
            `;
            messagesContainer.appendChild(messageDiv);
            scrollToBottom();
        }

        // Ajouter un message assistant avec formatage markdown
        function addAssistantMessage(content) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message assistant';

            // Formatage basique du markdown
            let formattedContent = content
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                .replace(/`(.*?)`/g, '<code>$1</code>')
                .replace(/^### (.*$)/gm, '<h3>$1</h3>')
                .replace(/^## (.*$)/gm, '<h2>$1</h2>')
                .replace(/^# (.*$)/gm, '<h1>$1</h1>')
                .replace(/^\* (.*$)/gm, '<li>$1</li>')
                .replace(/^- (.*$)/gm, '<li>$1</li>')
                .replace(/\n\n/g, '</p><p>')
                .replace(/^(?!<[h|l|p])/gm, '<p>')
                .replace(/(?<![h|i]>)$/gm, '</p>')
                .replace(/<p><\/p>/g, '')
                .replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>')
                .replace(/<\/ul>\s*<ul>/g, '');

            messageDiv.innerHTML = `
                <div class="message-content">${formattedContent}</div>
            `;
            messagesContainer.appendChild(messageDiv);
            scrollToBottom();
        }

        // Variable pour le message en streaming
        let currentStreamingMessage = null;

        // Mettre à jour le message en streaming
        function updateStreamingMessage(content, isComplete) {
            if (!currentStreamingMessage) {
                // Créer un nouveau message pour le streaming
                const messagesContainer = document.getElementById('chatMessages');
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message assistant';
                messageDiv.innerHTML = `
                    <div class="message-content">${formatContent(content)}</div>
                `;
                messagesContainer.appendChild(messageDiv);
                currentStreamingMessage = messageDiv.querySelector('.message-content');
            } else {
                // Mettre à jour le contenu existant
                currentStreamingMessage.innerHTML = formatContent(content);
            }

            if (isComplete) {
                currentStreamingMessage = null;
            }

            scrollToBottom();
        }

        // Ajouter un message système
        function addSystemMessage(content) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message system';
            messageDiv.innerHTML = `
                <div class="message-content">${escapeHtml(content)}</div>
            `;
            messagesContainer.appendChild(messageDiv);
            scrollToBottom();
        }

        // Animation de recherche style Perplexity avec étapes dynamiques
        let researchStartTime = null;
        let timerInterval = null;
        let stepCounter = 0;

        function showResearchStatus() {
            console.log('🎬 showResearchStatus() appelée');
            const status = document.getElementById('researchStatus');
            status.style.display = 'block';
            status.classList.add('active');
            console.log('🎬 Animation affichée, display:', status.style.display);

            // Réinitialiser
            resetDynamicSteps();

            // Démarrer le timer
            startResearchTimer();

            // Ajouter la première étape
            addDynamicStep('🔍', 'Analyse de la requête', 'active');
        }

        function hideResearchStatus() {
            const status = document.getElementById('researchStatus');
            status.style.display = 'none';
            status.classList.remove('active');

            // Arrêter le timer
            stopResearchTimer();

            // Masquer les sources
            document.getElementById('sourcesSection').style.display = 'none';
        }

        function resetDynamicSteps() {
            const stepsContainer = document.getElementById('dynamicSteps');
            stepsContainer.innerHTML = '';
            stepCounter = 0;
        }

        function addDynamicStep(icon, text, status = 'pending') {
            const stepsContainer = document.getElementById('dynamicSteps');
            const stepId = `step-${stepCounter++}`;

            const stepDiv = document.createElement('div');
            stepDiv.className = `dynamic-step ${status}`;
            stepDiv.id = stepId;
            stepDiv.innerHTML = `
                <div class="phase-icon">${icon}</div>
                <div class="phase-text">${text}</div>
            `;

            stepsContainer.appendChild(stepDiv);
            return stepId;
        }

        function updateStep(stepId, icon, text, status) {
            const step = document.getElementById(stepId);
            if (step) {
                step.className = `dynamic-step ${status}`;
                step.querySelector('.phase-icon').textContent = icon;
                step.querySelector('.phase-text').textContent = text;
            }
        }

        function completeCurrentStep() {
            const activeStep = document.querySelector('.dynamic-step.active');
            if (activeStep) {
                activeStep.classList.remove('active');
                activeStep.classList.add('completed');
                activeStep.querySelector('.phase-icon').textContent = '✅';
            }
        }

        function displaySources(data) {
            const sourcesSection = document.getElementById('sourcesSection');
            const sourcesGrid = document.getElementById('sourcesGrid');
            const sourcesCount = document.getElementById('sourcesCount');

            if (data.sources && data.sources.length > 0) {
                sourcesCount.textContent = `${data.sources.length} sources trouvées`;

                sourcesGrid.innerHTML = data.sources.map(source => `
                    <div class="source-item" onclick="window.open('${source.url}', '_blank')">
                        <div class="source-header">
                            <div class="source-id">#${source.id}</div>
                            <div class="source-engine">${source.engine}</div>
                            <div class="source-score">Score: ${source.score}</div>
                        </div>
                        <div class="source-title">${source.title}</div>
                        <div class="source-snippet">${source.snippet}</div>
                        <div class="source-url">${source.url}</div>
                    </div>
                `).join('');

                sourcesSection.style.display = 'block';
            }
        }

        function stopResearchAnimation(reason) {
            const researchStatus = document.getElementById('researchStatus');
            const researchTitle = document.getElementById('researchTitle');
            const researchSpinner = document.querySelector('.research-spinner');

            // Arrêter le timer
            if (researchTimer) {
                clearInterval(researchTimer);
            }

            // Changer le titre et arrêter l'animation
            researchTitle.textContent = `✅ Recherche terminée - ${reason}`;
            researchSpinner.style.animation = 'none';

            // Marquer toutes les étapes comme complétées
            const activeSteps = document.querySelectorAll('.dynamic-step.active');
            activeSteps.forEach(step => {
                step.classList.remove('active');
                step.classList.add('completed');
                step.querySelector('.phase-icon').textContent = '✅';
            });

            // Masquer l'animation après 3 secondes
            setTimeout(() => {
                researchStatus.style.display = 'none';
            }, 3000);
        }

        function startResearchTimer() {
            researchStartTime = Date.now();
            const timerElement = document.getElementById('researchTimer');

            timerInterval = setInterval(() => {
                const elapsed = Math.floor((Date.now() - researchStartTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;
                timerElement.textContent = minutes > 0 ? `${minutes}m ${seconds}s` : `${seconds}s`;
            }, 1000);
        }

        function stopResearchTimer() {
            if (timerInterval) {
                clearInterval(timerInterval);
                timerInterval = null;
            }
        }

        function updateProgressPhase(title) {
            // Mise à jour du titre de recherche
            const titleElement = document.querySelector('.research-title');
            if (titleElement) {
                titleElement.textContent = title;
            }
        }

        function addSourcesAnimation(sources) {
            const sourcesSection = document.getElementById('sourcesSection');
            const sourcesGrid = document.getElementById('sourcesGrid');
            const sourcesCount = document.getElementById('sourcesCount');

            if (sources && sources.length > 0) {
                sourcesSection.style.display = 'block';
                sourcesCount.textContent = `${sources.length} sources trouvées`;

                // Vider la grille
                sourcesGrid.innerHTML = '';

                // Ajouter chaque source avec animation
                sources.forEach((source, index) => {
                    setTimeout(() => {
                        const sourceCard = document.createElement('div');
                        sourceCard.className = 'source-card';
                        sourceCard.style.animationDelay = `${index * 0.1}s`;

                        const logo = getSourceLogo(source.url || source.title);

                        sourceCard.innerHTML = `
                            <div class="source-logo">${logo}</div>
                            <span>${source.title || source.url || 'Source'}</span>
                        `;

                        sourcesGrid.appendChild(sourceCard);
                    }, index * 100);
                });
            }
        }

        function getSourceLogo(url) {
            if (!url) return '📄';

            const domain = url.toLowerCase();
            if (domain.includes('wikipedia')) return 'W';
            if (domain.includes('github')) return 'G';
            if (domain.includes('stackoverflow')) return 'SO';
            if (domain.includes('reddit')) return 'R';
            if (domain.includes('youtube')) return 'Y';
            if (domain.includes('arxiv')) return 'A';
            if (domain.includes('medium')) return 'M';
            if (domain.includes('news')) return 'N';

            // Première lettre du domaine
            const match = domain.match(/\/\/([^\/]+)/);
            if (match) {
                return match[1].charAt(0).toUpperCase();
            }

            return '🌐';
        }

        // Mettre à jour les étapes de recherche
        function updateResearchSteps(steps) {
            const stepsContainer = document.getElementById('researchSteps');
            stepsContainer.innerHTML = '';

            steps.forEach(step => {
                const stepDiv = document.createElement('div');
                stepDiv.className = 'research-step';
                stepDiv.innerHTML = `
                    <div class="step-icon ${step.status}">${getStepIcon(step.status)}</div>
                    <div class="step-content">
                        <div class="step-title">${step.title}</div>
                        <div class="step-details">${getStepDetails(step.status)}</div>
                    </div>
                `;
                stepsContainer.appendChild(stepDiv);
            });
        }

        // Obtenir l'icône d'une étape
        function getStepIcon(status) {
            switch (status) {
                case 'completed': return '✓';
                case 'active': return '⟳';
                case 'pending': return '○';
                default: return '○';
            }
        }

        // Obtenir les détails d'une étape
        function getStepDetails(status) {
            switch (status) {
                case 'completed': return 'Terminé';
                case 'active': return 'En cours...';
                case 'pending': return 'En attente';
                default: return 'En attente';
            }
        }

        // Démarrer un nouveau chat
        function startNewChat() {
            currentChatId = Date.now().toString();
            document.getElementById('chatMessages').innerHTML = '';
            hideResearchStatus();

            // Créer la conversation vide côté serveur
            fetch('/api/chat/create', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ chatId: currentChatId })
            }).then(() => {
                // Recharger l'historique pour voir la nouvelle conversation
                setTimeout(() => {
                    loadChatHistory();
                }, 200);
            }).catch(error => {
                console.error('Erreur création conversation:', error);
            });

            console.log(`✨ Nouvelle conversation créée: ${currentChatId}`);
        }

        // Échapper le HTML
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Faire défiler vers le bas
        function scrollToBottom() {
            const messagesContainer = document.getElementById('chatMessages');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Fonctions pour les nouveaux éléments
        function triggerFileUpload() {
            document.getElementById('fileInput').click();
        }

        function handleFileUpload(event) {
            const files = event.target.files;
            if (files.length > 0) {
                // Afficher les fichiers sélectionnés
                const fileNames = Array.from(files).map(f => f.name).join(', ');
                addSystemMessage(`📎 Fichiers sélectionnés: ${fileNames}`);

                // TODO: Implémenter l'upload et l'OCR
                console.log('Fichiers à traiter:', files);
            }
        }

        function getCurrentModel() {
            const activeBtn = document.querySelector('.model-btn.active');
            return activeBtn ? activeBtn.dataset.model : 'google/gemini-2.0-flash-exp:free';
        }

        function selectModel(button) {
            // Retirer la classe active de tous les boutons
            document.querySelectorAll('.model-btn').forEach(btn => btn.classList.remove('active'));
            // Ajouter la classe active au bouton cliqué
            button.classList.add('active');
        }

        function isSearchEnabled() {
            return true; // Toujours activé pour notre app
        }

        function stopMessage() {
            // TODO: Implémenter l'arrêt du message
            console.log('Arrêt du message demandé');
            resetSendButton();
        }

        function resetSendButton() {
            const sendButton = document.getElementById('sendButton');
            sendButton.classList.remove('sending');
            sendButton.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="22" y1="2" x2="11" y2="13"></line>
                    <polygon points="22,2 15,22 11,13 2,9 22,2"></polygon>
                </svg>
            `;
            sendButton.title = "Envoyer";
            sendButton.onclick = sendMessage;
        }

        // Charger l'historique des conversations
        async function loadChatHistory() {
            try {
                const response = await fetch('/api/chat-history');
                const history = await response.json();

                const historyContainer = document.getElementById('chatHistory');
                historyContainer.innerHTML = '';

                history.forEach(chat => {
                    const chatItem = document.createElement('div');
                    chatItem.className = 'chat-item';
                    chatItem.innerHTML = `
                        <div class="chat-title">${chat.title}</div>
                        <div class="chat-date">${new Date(chat.updatedAt).toLocaleDateString('fr-FR')}</div>
                    `;
                    chatItem.onclick = () => loadChat(chat.id);
                    historyContainer.appendChild(chatItem);
                });

                console.log(`📚 ${history.length} conversations chargées`);
            } catch (error) {
                console.error('Erreur chargement historique:', error);
            }
        }

        // Charger une conversation spécifique
        async function loadChat(chatId) {
            try {
                const response = await fetch(`/api/chat/${chatId}`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const chat = await response.json();

                if (!chat || !chat.messages) {
                    throw new Error('Format de conversation invalide');
                }

                currentChatId = chatId;
                const messagesContainer = document.getElementById('chatMessages');
                messagesContainer.innerHTML = '';

                chat.messages.forEach(msg => {
                    if (msg.role === 'user') {
                        addUserMessage(msg.content);
                    } else {
                        addAssistantMessage(msg.content);
                    }
                });

                console.log(`💬 Conversation ${chatId} chargée avec ${chat.messages.length} messages`);
            } catch (error) {
                console.error('Erreur chargement conversation:', error);
                alert(`Impossible de charger la conversation: ${error.message}`);
            }
        }

        // Initialisation au chargement
        document.addEventListener('DOMContentLoaded', function() {
            // Le modèle par défaut est déjà sélectionné (Flash)
        });
    </script>
</body>
</html>
